{"artifacts": [{"path": "plugins/local_auth_windows/local_auth_windows_plugin.dll"}, {"path": "plugins/local_auth_windows/local_auth_windows_plugin.lib"}, {"path": "plugins/local_auth_windows/local_auth_windows_plugin.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_dependencies", "target_compile_options", "apply_standard_settings", "target_compile_definitions", "add_definitions", "include_directories", "target_compile_features"], "files": ["flutter/ephemeral/.plugin_symlinks/local_auth_windows/windows/CMakeLists.txt", "flutter/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 58, "parent": 0}, {"command": 1, "file": 0, "line": 70, "parent": 0}, {"command": 1, "file": 0, "line": 71, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 40, "parent": 4}, {"command": 4, "file": 0, "line": 63, "parent": 0}, {"command": 3, "file": 2, "line": 42, "parent": 6}, {"command": 3, "file": 2, "line": 43, "parent": 6}, {"command": 3, "file": 0, "line": 66, "parent": 0}, {"command": 5, "file": 0, "line": 67, "parent": 0}, {"file": 2}, {"command": 6, "file": 2, "line": 33, "parent": 11}, {"command": 5, "file": 2, "line": 45, "parent": 6}, {"command": 5, "file": 2, "line": 44, "parent": 6}, {"command": 7, "file": 0, "line": 49, "parent": 0}, {"command": 8, "file": 2, "line": 41, "parent": 6}, {"command": 8, "file": 0, "line": 65, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1"}, {"backtrace": 7, "fragment": "/W4"}, {"backtrace": 7, "fragment": "/WX"}, {"backtrace": 7, "fragment": "/wd\\\"4100\\\""}, {"backtrace": 8, "fragment": "/EHsc"}, {"backtrace": 9, "fragment": "/await"}, {"fragment": "-std:c++latest"}], "defines": [{"backtrace": 10, "define": "FLUTTER_PLUGIN_IMPL"}, {"backtrace": 12, "define": "UNICODE"}, {"backtrace": 13, "define": "_DEBUG"}, {"backtrace": 14, "define": "_HAS_EXCEPTIONS=0"}, {"backtrace": 12, "define": "_UNICODE"}, {"define": "local_auth_windows_plugin_EXPORTS"}], "includes": [{"backtrace": 3, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral"}, {"backtrace": 3, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/cpp_client_wrapper/include"}, {"backtrace": 15, "isSystem": true, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/out/build/x64-Debug/include"}], "language": "CXX", "languageStandard": {"backtraces": [16, 17], "standard": "20"}, "sourceIndexes": [1, 2, 4]}], "dependencies": [{"backtrace": 5, "id": "flutter_assemble::@2bb09fbb03a373b2cb34"}, {"backtrace": 3, "id": "flutter_wrapper_plugin::@2bb09fbb03a373b2cb34"}], "id": "local_auth_windows_plugin::@bb74027535e939cb2603", "link": {"commandFragments": [{"fragment": "/machine:x64 /debug /INCREMENTAL", "role": "flags"}, {"backtrace": 2, "fragment": "..\\..\\packages\\Microsoft.Windows.ImplementationLibrary.1.0.220201.1\\build\\native\\Microsoft.Windows.ImplementationLibrary.targets", "role": "libraries"}, {"backtrace": 3, "fragment": "..\\..\\flutter\\flutter_wrapper_plugin.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "windowsapp.lib", "role": "libraries"}, {"fragment": "..\\..\\..\\..\\..\\flutter\\ephemeral\\flutter_windows.dll.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "local_auth_windows_plugin", "nameOnDisk": "local_auth_windows_plugin.dll", "paths": {"build": "plugins/local_auth_windows", "source": "flutter/ephemeral/.plugin_symlinks/local_auth_windows/windows"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 3, 5]}, {"name": "Source Files", "sourceIndexes": [1, 2, 4]}], "sources": [{"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/local_auth_windows/windows/include/local_auth_windows/local_auth_plugin.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/local_auth_windows/windows/local_auth_windows.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/local_auth_windows/windows/local_auth_plugin.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/local_auth_windows/windows/local_auth.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/local_auth_windows/windows/messages.g.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/local_auth_windows/windows/messages.g.h", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}