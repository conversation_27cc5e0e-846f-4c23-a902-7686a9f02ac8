{"artifacts": [{"path": "plugins/permission_handler_windows/permission_handler_windows_plugin.dll"}, {"path": "plugins/permission_handler_windows/permission_handler_windows_plugin.lib"}, {"path": "plugins/permission_handler_windows/permission_handler_windows_plugin.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_dependencies", "target_compile_options", "apply_standard_settings", "target_compile_definitions", "add_definitions", "include_directories", "target_compile_features"], "files": ["flutter/ephemeral/.plugin_symlinks/permission_handler_windows/windows/CMakeLists.txt", "flutter/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 43, "parent": 0}, {"command": 1, "file": 0, "line": 54, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 40, "parent": 3}, {"command": 4, "file": 0, "line": 47, "parent": 0}, {"command": 3, "file": 2, "line": 42, "parent": 5}, {"command": 3, "file": 2, "line": 43, "parent": 5}, {"command": 3, "file": 0, "line": 50, "parent": 0}, {"command": 5, "file": 0, "line": 51, "parent": 0}, {"file": 2}, {"command": 6, "file": 2, "line": 33, "parent": 10}, {"command": 5, "file": 2, "line": 45, "parent": 5}, {"command": 5, "file": 2, "line": 44, "parent": 5}, {"command": 7, "file": 0, "line": 41, "parent": 0}, {"command": 8, "file": 2, "line": 41, "parent": 5}, {"command": 8, "file": 0, "line": 49, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1"}, {"backtrace": 6, "fragment": "/W4"}, {"backtrace": 6, "fragment": "/WX"}, {"backtrace": 6, "fragment": "/wd\\\"4100\\\""}, {"backtrace": 7, "fragment": "/EHsc"}, {"backtrace": 8, "fragment": "/await"}, {"fragment": "-std:c++latest"}], "defines": [{"backtrace": 9, "define": "FLUTTER_PLUGIN_IMPL"}, {"backtrace": 11, "define": "UNICODE"}, {"backtrace": 12, "define": "_DEBUG"}, {"backtrace": 13, "define": "_HAS_EXCEPTIONS=0"}, {"backtrace": 11, "define": "_UNICODE"}, {"define": "permission_handler_windows_plugin_EXPORTS"}], "includes": [{"backtrace": 2, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral"}, {"backtrace": 2, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/cpp_client_wrapper/include"}, {"backtrace": 14, "isSystem": true, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/out/build/x64-Debug/include"}], "language": "CXX", "languageStandard": {"backtraces": [15, 16], "standard": "20"}, "sourceIndexes": [1]}], "dependencies": [{"backtrace": 4, "id": "flutter_assemble::@2bb09fbb03a373b2cb34"}, {"backtrace": 2, "id": "flutter_wrapper_plugin::@2bb09fbb03a373b2cb34"}], "id": "permission_handler_windows_plugin::@15851dc22c812b45487c", "link": {"commandFragments": [{"fragment": "/machine:x64 /debug /INCREMENTAL", "role": "flags"}, {"backtrace": 2, "fragment": "..\\..\\flutter\\flutter_wrapper_plugin.lib", "role": "libraries"}, {"fragment": "..\\..\\..\\..\\..\\flutter\\ephemeral\\flutter_windows.dll.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "permission_handler_windows_plugin", "nameOnDisk": "permission_handler_windows_plugin.dll", "paths": {"build": "plugins/permission_handler_windows", "source": "flutter/ephemeral/.plugin_symlinks/permission_handler_windows/windows"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0]}, {"name": "Source Files", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/permission_handler_windows/windows/include/permission_handler_windows/permission_handler_windows_plugin.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/permission_handler_windows/windows/permission_handler_windows_plugin.cpp", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}