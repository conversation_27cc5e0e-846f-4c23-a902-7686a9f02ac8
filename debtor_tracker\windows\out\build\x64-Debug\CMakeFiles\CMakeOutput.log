The system is: Windows - 10.0.26100 - AMD64
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/cl.exe 
Build flags: 
Id flags:  

The output was:
0
Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30154 for x64
Copyright (C) Microsoft Corporation.  All rights reserved.

CMakeCXXCompilerId.cpp
Microsoft (R) Incremental Linker Version 14.29.30154.0
Copyright (C) Microsoft Corporation.  All rights reserved.

/out:CMakeCXXCompilerId.exe 
CMakeCXXCompilerId.obj 


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.exe"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.obj"

The CXX compiler identification is MSVC, found in "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/out/build/x64-Debug/CMakeFiles/3.20.21032501-MSVC_2/CompilerIdCXX/CMakeCXXCompilerId.exe"

Detecting CXX compiler ABI info compiled with the following output:
Change Dir: C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/out/build/x64-Debug/CMakeFiles/CMakeTmp

Run Build Command(s):C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe cmTC_9562a && [1/2] Building CXX object CMakeFiles\cmTC_9562a.dir\CMakeCXXCompilerABI.cpp.obj

[2/2] Linking CXX executable cmTC_9562a.exe




