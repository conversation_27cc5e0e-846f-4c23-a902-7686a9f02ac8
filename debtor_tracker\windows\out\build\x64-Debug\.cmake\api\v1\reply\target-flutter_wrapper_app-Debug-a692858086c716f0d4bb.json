{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "flutter/flutter_wrapper_app.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_dependencies", "target_compile_options", "apply_standard_settings", "add_definitions", "target_compile_definitions", "target_include_directories", "target_link_libraries", "target_compile_features"], "files": ["flutter/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 75, "parent": 0}, {"command": 1, "file": 0, "line": 84, "parent": 0}, {"command": 3, "file": 0, "line": 79, "parent": 0}, {"command": 2, "file": 1, "line": 42, "parent": 3}, {"command": 2, "file": 1, "line": 43, "parent": 3}, {"file": 1}, {"command": 4, "file": 1, "line": 33, "parent": 6}, {"command": 5, "file": 1, "line": 45, "parent": 3}, {"command": 5, "file": 1, "line": 44, "parent": 3}, {"command": 6, "file": 0, "line": 81, "parent": 0}, {"command": 7, "file": 0, "line": 80, "parent": 0}, {"command": 8, "file": 1, "line": 41, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1"}, {"backtrace": 4, "fragment": "/W4"}, {"backtrace": 4, "fragment": "/WX"}, {"backtrace": 4, "fragment": "/wd\\\"4100\\\""}, {"backtrace": 5, "fragment": "/EHsc"}, {"fragment": "-std:c++17"}], "defines": [{"backtrace": 7, "define": "UNICODE"}, {"backtrace": 8, "define": "_DEBUG"}, {"backtrace": 9, "define": "_HAS_EXCEPTIONS=0"}, {"backtrace": 7, "define": "_UNICODE"}], "includes": [{"backtrace": 10, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/cpp_client_wrapper/include"}, {"backtrace": 11, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral"}], "language": "CXX", "languageStandard": {"backtraces": [12], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3]}], "dependencies": [{"backtrace": 2, "id": "flutter_assemble::@2bb09fbb03a373b2cb34"}], "id": "flutter_wrapper_app::@2bb09fbb03a373b2cb34", "name": "flutter_wrapper_app", "nameOnDisk": "flutter_wrapper_app.lib", "paths": {"build": "flutter", "source": "flutter"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3]}, {"name": "CMake Rules", "sourceIndexes": [4]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "isGenerated": true, "path": "flutter/ephemeral/cpp_client_wrapper/core_implementations.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "isGenerated": true, "path": "flutter/ephemeral/cpp_client_wrapper/standard_codec.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "isGenerated": true, "path": "flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "isGenerated": true, "path": "flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "flutter/ephemeral/flutter_windows.dll.rule", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}