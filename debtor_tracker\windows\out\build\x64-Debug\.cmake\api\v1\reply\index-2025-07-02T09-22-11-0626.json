{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe", "cpack": "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cpack.exe", "ctest": "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/ctest.exe", "root": "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.20"}, "version": {"isDirty": false, "major": 3, "minor": 20, "patch": 21032501, "string": "3.20.21032501-MSVC_2", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-20aaa6105a0ca68f80f4.json", "kind": "codemodel", "version": {"major": 2, "minor": 2}}, {"jsonFile": "cache-v2-4763b72ad2a0c1e7c1e6.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-110179fcc1793990f24f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-341b3005a90deda3fcaa.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-MicrosoftVS": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "cmakeFiles", "version": 1}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}], "responses": [{"jsonFile": "cache-v2-4763b72ad2a0c1e7c1e6.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-110179fcc1793990f24f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "codemodel-v2-20aaa6105a0ca68f80f4.json", "kind": "codemodel", "version": {"major": 2, "minor": 2}}, {"jsonFile": "toolchains-v1-341b3005a90deda3fcaa.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}]}}}}