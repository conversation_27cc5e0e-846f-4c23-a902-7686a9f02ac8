{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], "hasInstallRule": true, "minimumCMakeVersion": {"string": "3.14"}, "projectIndex": 0, "source": "."}, {"build": "flutter", "minimumCMakeVersion": {"string": "3.14"}, "parentIndex": 0, "projectIndex": 0, "source": "flutter", "targetIndexes": [4, 6, 7]}, {"build": "runner", "minimumCMakeVersion": {"string": "3.14"}, "parentIndex": 0, "projectIndex": 1, "source": "runner", "targetIndexes": [2]}, {"build": "plugins/audioplayers_windows", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 0, "projectIndex": 2, "source": "flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows", "targetIndexes": [0]}, {"build": "plugins/connectivity_plus", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 0, "projectIndex": 3, "source": "flutter/ephemeral/.plugin_symlinks/connectivity_plus/windows", "targetIndexes": [1]}, {"build": "plugins/file_selector_windows", "minimumCMakeVersion": {"string": "3.14"}, "parentIndex": 0, "projectIndex": 4, "source": "flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows", "targetIndexes": [3]}, {"build": "plugins/flutter_secure_storage_windows", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 0, "projectIndex": 5, "source": "flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_windows/windows", "targetIndexes": [5]}, {"build": "plugins/gal", "minimumCMakeVersion": {"string": "3.14"}, "parentIndex": 0, "projectIndex": 6, "source": "flutter/ephemeral/.plugin_symlinks/gal/windows", "targetIndexes": [8]}, {"build": "plugins/local_auth_windows", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 0, "projectIndex": 7, "source": "flutter/ephemeral/.plugin_symlinks/local_auth_windows/windows", "targetIndexes": [9]}, {"build": "plugins/permission_handler_windows", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 0, "projectIndex": 8, "source": "flutter/ephemeral/.plugin_symlinks/permission_handler_windows/windows", "targetIndexes": [10]}, {"build": "plugins/printing", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 0, "projectIndex": 9, "source": "flutter/ephemeral/.plugin_symlinks/printing/windows", "targetIndexes": [11]}, {"build": "plugins/share_plus", "minimumCMakeVersion": {"string": "3.14"}, "parentIndex": 0, "projectIndex": 10, "source": "flutter/ephemeral/.plugin_symlinks/share_plus/windows", "targetIndexes": [12]}, {"build": "plugins/url_launcher_windows", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 11, "source": "flutter/ephemeral/.plugin_symlinks/url_launcher_windows/windows", "targetIndexes": [13]}], "name": "Debug", "projects": [{"childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "directoryIndexes": [0, 1], "name": "debtor_tracker", "targetIndexes": [4, 6, 7]}, {"directoryIndexes": [2], "name": "runner", "parentIndex": 0, "targetIndexes": [2]}, {"directoryIndexes": [3], "name": "audioplayers_windows", "parentIndex": 0, "targetIndexes": [0]}, {"directoryIndexes": [4], "name": "connectivity_plus", "parentIndex": 0, "targetIndexes": [1]}, {"directoryIndexes": [5], "name": "file_selector_windows", "parentIndex": 0, "targetIndexes": [3]}, {"directoryIndexes": [6], "name": "flutter_secure_storage_windows", "parentIndex": 0, "targetIndexes": [5]}, {"directoryIndexes": [7], "name": "gal", "parentIndex": 0, "targetIndexes": [8]}, {"directoryIndexes": [8], "name": "local_auth_windows", "parentIndex": 0, "targetIndexes": [9]}, {"directoryIndexes": [9], "name": "permission_handler_windows", "parentIndex": 0, "targetIndexes": [10]}, {"directoryIndexes": [10], "name": "printing", "parentIndex": 0, "targetIndexes": [11]}, {"directoryIndexes": [11], "name": "share_plus", "parentIndex": 0, "targetIndexes": [12]}, {"directoryIndexes": [12], "name": "url_launcher_windows", "parentIndex": 0, "targetIndexes": [13]}], "targets": [{"directoryIndex": 3, "id": "audioplayers_windows_plugin::@6f00b843ae6f76675cd2", "jsonFile": "target-audioplayers_windows_plugin-Debug-c5e9e201aa938a7d15f6.json", "name": "audioplayers_windows_plugin", "projectIndex": 2}, {"directoryIndex": 4, "id": "connectivity_plus_plugin::@6eaccbd1f84c59b7458d", "jsonFile": "target-connectivity_plus_plugin-Debug-00567443e191eeac1838.json", "name": "connectivity_plus_plugin", "projectIndex": 3}, {"directoryIndex": 2, "id": "debtor_tracker::@056c4f61b0156d1d9b06", "jsonFile": "target-debtor_tracker-Debug-49f7099fea9603127ea6.json", "name": "debtor_tracker", "projectIndex": 1}, {"directoryIndex": 5, "id": "file_selector_windows_plugin::@24329985f05541c6c489", "jsonFile": "target-file_selector_windows_plugin-Debug-4ac61990c07183db260a.json", "name": "file_selector_windows_plugin", "projectIndex": 4}, {"directoryIndex": 1, "id": "flutter_assemble::@2bb09fbb03a373b2cb34", "jsonFile": "target-flutter_assemble-Debug-29f9f19596fc4e2071dc.json", "name": "flutter_assemble", "projectIndex": 0}, {"directoryIndex": 6, "id": "flutter_secure_storage_windows_plugin::@ef50a4a009d72e8219ec", "jsonFile": "target-flutter_secure_storage_windows_plugin-Debug-f31446dd9cba5b37a01b.json", "name": "flutter_secure_storage_windows_plugin", "projectIndex": 5}, {"directoryIndex": 1, "id": "flutter_wrapper_app::@2bb09fbb03a373b2cb34", "jsonFile": "target-flutter_wrapper_app-Debug-a692858086c716f0d4bb.json", "name": "flutter_wrapper_app", "projectIndex": 0}, {"directoryIndex": 1, "id": "flutter_wrapper_plugin::@2bb09fbb03a373b2cb34", "jsonFile": "target-flutter_wrapper_plugin-Debug-116cad49250ed67c4359.json", "name": "flutter_wrapper_plugin", "projectIndex": 0}, {"directoryIndex": 7, "id": "gal_plugin::@48c3c4013a15203650f9", "jsonFile": "target-gal_plugin-Debug-6c8096d426e1c759ca36.json", "name": "gal_plugin", "projectIndex": 6}, {"directoryIndex": 8, "id": "local_auth_windows_plugin::@bb74027535e939cb2603", "jsonFile": "target-local_auth_windows_plugin-Debug-5c349b678094eccf7e23.json", "name": "local_auth_windows_plugin", "projectIndex": 7}, {"directoryIndex": 9, "id": "permission_handler_windows_plugin::@15851dc22c812b45487c", "jsonFile": "target-permission_handler_windows_plugin-Debug-2e17a075c2b5d6780d97.json", "name": "permission_handler_windows_plugin", "projectIndex": 8}, {"directoryIndex": 10, "id": "printing_plugin::@3f1d3ea8e3fd99684a01", "jsonFile": "target-printing_plugin-Debug-797f179a05749993b1c9.json", "name": "printing_plugin", "projectIndex": 9}, {"directoryIndex": 11, "id": "share_plus_plugin::@1b881e75852d57e06a99", "jsonFile": "target-share_plus_plugin-Debug-eb94907724464ff9d6fd.json", "name": "share_plus_plugin", "projectIndex": 10}, {"directoryIndex": 12, "id": "url_launcher_windows_plugin::@82e8e937a0da8e6132e4", "jsonFile": "target-url_launcher_windows_plugin-Debug-d62e28831aff73ce11c3.json", "name": "url_launcher_windows_plugin", "projectIndex": 11}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/out/build/x64-Debug", "source": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows"}, "version": {"major": 2, "minor": 2}}