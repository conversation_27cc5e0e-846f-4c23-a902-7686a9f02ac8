// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Services_Maps_Guidance_1_H
#define WINRT_Windows_Services_Maps_Guidance_1_H
#include "winrt/impl/Windows.Services.Maps.Guidance.0.h"
WINRT_EXPORT namespace winrt::Windows::Services::Maps::Guidance
{
    struct __declspec(empty_bases) IGuidanceAudioNotificationRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceAudioNotificationRequestedEventArgs>
    {
        IGuidanceAudioNotificationRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IGuidanceAudioNotificationRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceLaneInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceLaneInfo>
    {
        IGuidanceLaneInfo(std::nullptr_t = nullptr) noexcept {}
        IGuidanceLaneInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceManeuver :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceManeuver>
    {
        IGuidanceManeuver(std::nullptr_t = nullptr) noexcept {}
        IGuidanceManeuver(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceMapMatchedCoordinate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceMapMatchedCoordinate>
    {
        IGuidanceMapMatchedCoordinate(std::nullptr_t = nullptr) noexcept {}
        IGuidanceMapMatchedCoordinate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceNavigator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceNavigator>
    {
        IGuidanceNavigator(std::nullptr_t = nullptr) noexcept {}
        IGuidanceNavigator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceNavigator2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceNavigator2>
    {
        IGuidanceNavigator2(std::nullptr_t = nullptr) noexcept {}
        IGuidanceNavigator2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceNavigatorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceNavigatorStatics>
    {
        IGuidanceNavigatorStatics(std::nullptr_t = nullptr) noexcept {}
        IGuidanceNavigatorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceNavigatorStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceNavigatorStatics2>
    {
        IGuidanceNavigatorStatics2(std::nullptr_t = nullptr) noexcept {}
        IGuidanceNavigatorStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceReroutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceReroutedEventArgs>
    {
        IGuidanceReroutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IGuidanceReroutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceRoadSegment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceRoadSegment>
    {
        IGuidanceRoadSegment(std::nullptr_t = nullptr) noexcept {}
        IGuidanceRoadSegment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceRoadSegment2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceRoadSegment2>
    {
        IGuidanceRoadSegment2(std::nullptr_t = nullptr) noexcept {}
        IGuidanceRoadSegment2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceRoadSignpost :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceRoadSignpost>
    {
        IGuidanceRoadSignpost(std::nullptr_t = nullptr) noexcept {}
        IGuidanceRoadSignpost(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceRoute :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceRoute>
    {
        IGuidanceRoute(std::nullptr_t = nullptr) noexcept {}
        IGuidanceRoute(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceRouteStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceRouteStatics>
    {
        IGuidanceRouteStatics(std::nullptr_t = nullptr) noexcept {}
        IGuidanceRouteStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceTelemetryCollector :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceTelemetryCollector>
    {
        IGuidanceTelemetryCollector(std::nullptr_t = nullptr) noexcept {}
        IGuidanceTelemetryCollector(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceTelemetryCollectorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceTelemetryCollectorStatics>
    {
        IGuidanceTelemetryCollectorStatics(std::nullptr_t = nullptr) noexcept {}
        IGuidanceTelemetryCollectorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGuidanceUpdatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGuidanceUpdatedEventArgs>
    {
        IGuidanceUpdatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IGuidanceUpdatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
