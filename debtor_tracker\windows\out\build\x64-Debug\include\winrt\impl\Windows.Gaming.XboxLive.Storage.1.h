// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Gaming_XboxLive_Storage_1_H
#define WINRT_Windows_Gaming_XboxLive_Storage_1_H
#include "winrt/impl/Windows.Gaming.XboxLive.Storage.0.h"
WINRT_EXPORT namespace winrt::Windows::Gaming::XboxLive::Storage
{
    struct __declspec(empty_bases) IGameSaveBlobGetResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameSaveBlobGetResult>
    {
        IGameSaveBlobGetResult(std::nullptr_t = nullptr) noexcept {}
        IGameSaveBlobGetResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGameSaveBlobInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameSaveBlobInfo>
    {
        IGameSaveBlobInfo(std::nullptr_t = nullptr) noexcept {}
        IGameSaveBlobInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGameSaveBlobInfoGetResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameSaveBlobInfoGetResult>
    {
        IGameSaveBlobInfoGetResult(std::nullptr_t = nullptr) noexcept {}
        IGameSaveBlobInfoGetResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGameSaveBlobInfoQuery :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameSaveBlobInfoQuery>
    {
        IGameSaveBlobInfoQuery(std::nullptr_t = nullptr) noexcept {}
        IGameSaveBlobInfoQuery(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGameSaveContainer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameSaveContainer>
    {
        IGameSaveContainer(std::nullptr_t = nullptr) noexcept {}
        IGameSaveContainer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGameSaveContainerInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameSaveContainerInfo>
    {
        IGameSaveContainerInfo(std::nullptr_t = nullptr) noexcept {}
        IGameSaveContainerInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGameSaveContainerInfoGetResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameSaveContainerInfoGetResult>
    {
        IGameSaveContainerInfoGetResult(std::nullptr_t = nullptr) noexcept {}
        IGameSaveContainerInfoGetResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGameSaveContainerInfoQuery :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameSaveContainerInfoQuery>
    {
        IGameSaveContainerInfoQuery(std::nullptr_t = nullptr) noexcept {}
        IGameSaveContainerInfoQuery(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGameSaveOperationResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameSaveOperationResult>
    {
        IGameSaveOperationResult(std::nullptr_t = nullptr) noexcept {}
        IGameSaveOperationResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGameSaveProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameSaveProvider>
    {
        IGameSaveProvider(std::nullptr_t = nullptr) noexcept {}
        IGameSaveProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGameSaveProviderGetResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameSaveProviderGetResult>
    {
        IGameSaveProviderGetResult(std::nullptr_t = nullptr) noexcept {}
        IGameSaveProviderGetResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGameSaveProviderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGameSaveProviderStatics>
    {
        IGameSaveProviderStatics(std::nullptr_t = nullptr) noexcept {}
        IGameSaveProviderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
