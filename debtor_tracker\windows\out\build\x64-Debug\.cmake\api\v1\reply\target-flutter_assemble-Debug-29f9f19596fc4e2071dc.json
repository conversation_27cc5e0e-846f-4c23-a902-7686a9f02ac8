{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["flutter/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 103, "parent": 0}]}, "id": "flutter_assemble::@2bb09fbb03a373b2cb34", "name": "flutter_assemble", "paths": {"build": "flutter", "source": "flutter"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "out/build/x64-Debug/flutter/CMakeFiles/flutter_assemble", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "out/build/x64-Debug/flutter/CMakeFiles/flutter_assemble.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "flutter/ephemeral/flutter_windows.dll.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}