// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_ApplicationModel_UserActivities_1_H
#define WINRT_Windows_ApplicationModel_UserActivities_1_H
#include "winrt/impl/Windows.ApplicationModel.UserActivities.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::UserActivities
{
    struct __declspec(empty_bases) IUserActivity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivity>
    {
        IUserActivity(std::nullptr_t = nullptr) noexcept {}
        IUserActivity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivity2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivity2>
    {
        IUserActivity2(std::nullptr_t = nullptr) noexcept {}
        IUserActivity2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivity3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivity3>
    {
        IUserActivity3(std::nullptr_t = nullptr) noexcept {}
        IUserActivity3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityAttribution :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityAttribution>
    {
        IUserActivityAttribution(std::nullptr_t = nullptr) noexcept {}
        IUserActivityAttribution(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityAttributionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityAttributionFactory>
    {
        IUserActivityAttributionFactory(std::nullptr_t = nullptr) noexcept {}
        IUserActivityAttributionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityChannel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityChannel>
    {
        IUserActivityChannel(std::nullptr_t = nullptr) noexcept {}
        IUserActivityChannel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityChannel2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityChannel2>
    {
        IUserActivityChannel2(std::nullptr_t = nullptr) noexcept {}
        IUserActivityChannel2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityChannelStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityChannelStatics>
    {
        IUserActivityChannelStatics(std::nullptr_t = nullptr) noexcept {}
        IUserActivityChannelStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityChannelStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityChannelStatics2>
    {
        IUserActivityChannelStatics2(std::nullptr_t = nullptr) noexcept {}
        IUserActivityChannelStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityChannelStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityChannelStatics3>
    {
        IUserActivityChannelStatics3(std::nullptr_t = nullptr) noexcept {}
        IUserActivityChannelStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityContentInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityContentInfo>
    {
        IUserActivityContentInfo(std::nullptr_t = nullptr) noexcept {}
        IUserActivityContentInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityContentInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityContentInfoStatics>
    {
        IUserActivityContentInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IUserActivityContentInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityFactory>
    {
        IUserActivityFactory(std::nullptr_t = nullptr) noexcept {}
        IUserActivityFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityRequest>
    {
        IUserActivityRequest(std::nullptr_t = nullptr) noexcept {}
        IUserActivityRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityRequestManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityRequestManager>
    {
        IUserActivityRequestManager(std::nullptr_t = nullptr) noexcept {}
        IUserActivityRequestManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityRequestManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityRequestManagerStatics>
    {
        IUserActivityRequestManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IUserActivityRequestManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityRequestedEventArgs>
    {
        IUserActivityRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IUserActivityRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivitySession :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivitySession>
    {
        IUserActivitySession(std::nullptr_t = nullptr) noexcept {}
        IUserActivitySession(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivitySessionHistoryItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivitySessionHistoryItem>
    {
        IUserActivitySessionHistoryItem(std::nullptr_t = nullptr) noexcept {}
        IUserActivitySessionHistoryItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityStatics>
    {
        IUserActivityStatics(std::nullptr_t = nullptr) noexcept {}
        IUserActivityStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityVisualElements :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityVisualElements>
    {
        IUserActivityVisualElements(std::nullptr_t = nullptr) noexcept {}
        IUserActivityVisualElements(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUserActivityVisualElements2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserActivityVisualElements2>
    {
        IUserActivityVisualElements2(std::nullptr_t = nullptr) noexcept {}
        IUserActivityVisualElements2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
