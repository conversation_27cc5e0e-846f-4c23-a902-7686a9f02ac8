import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/google_drive_service.dart';
// افترض وجود Provider لإدارة بيانات المدينين
// import '../providers/debtor_provider.dart';

class BackupScreen extends StatefulWidget {
  const BackupScreen({super.key});

  @override
  State<BackupScreen> createState() => _BackupScreenState();
}

class _BackupScreenState extends State<BackupScreen> {
  final GoogleDriveService _driveService = GoogleDriveService();
  bool _isLoading = false;
  String _statusMessage = 'جاهز لعمل نسخة احتياطية';

  Future<void> _performBackup() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري تسجيل الدخول إلى حساب جوجل...';
    });

    final googleUser = await _driveService.signIn();
    if (googleUser == null) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'فشل تسجيل الدخول. يرجى المحاولة مرة أخرى.';
      });
      return;
    }

    setState(() {
      _statusMessage = 'جاري تجهيز البيانات للنسخ الاحتياطي...';
    });

    // هنا يجب أن تحصل على بياناتك لتحويلها إلى JSON
    // final debtorProvider = Provider.of<DebtorProvider>(context, listen: false);
    // final backupData = debtorProvider.toJson(); // افترض وجود دالة لتحويل البيانات
    const String backupData =
        '{"message": "This is a test backup"}'; // بيانات وهمية للتجربة

    setState(() {
      _statusMessage = 'جاري رفع النسخة الاحتياطية إلى Google Drive...';
    });

    final fileId = await _driveService.uploadBackup(backupData);

    setState(() {
      _isLoading = false;
      if (fileId != null) {
        _statusMessage = 'تم إنشاء النسخة الاحتياطية بنجاح!';
      } else {
        _statusMessage = 'فشل إنشاء النسخة الاحتياطية.';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('النسخ الاحتياطي والاستعادة')),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.cloud_done_rounded,
                size: 100,
                color: Colors.blue,
              ),
              const SizedBox(height: 20),
              const Text(
                'حماية بياناتك',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const Text(
                'قم بإنشاء نسخة احتياطية آمنة لبياناتك على حسابك في Google Drive.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              if (_isLoading)
                const CircularProgressIndicator()
              else
                ElevatedButton.icon(
                  onPressed: _performBackup,
                  icon: const Icon(Icons.backup_rounded),
                  label: const Text('إنشاء نسخة احتياطية الآن'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 30,
                      vertical: 15,
                    ),
                  ),
                ),
              const SizedBox(height: 20),
              Text(_statusMessage),
            ],
          ),
        ),
      ),
    );
  }
}
