// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Devices_Lights_Effects_1_H
#define WINRT_Windows_Devices_Lights_Effects_1_H
#include "winrt/impl/Windows.Devices.Lights.Effects.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Lights::Effects
{
    struct __declspec(empty_bases) ILampArrayBitmapEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayBitmapEffect>
    {
        ILampArrayBitmapEffect(std::nullptr_t = nullptr) noexcept {}
        ILampArrayBitmapEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArrayBitmapEffectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayBitmapEffectFactory>
    {
        ILampArrayBitmapEffectFactory(std::nullptr_t = nullptr) noexcept {}
        ILampArrayBitmapEffectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArrayBitmapRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayBitmapRequestedEventArgs>
    {
        ILampArrayBitmapRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ILampArrayBitmapRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArrayBlinkEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayBlinkEffect>
    {
        ILampArrayBlinkEffect(std::nullptr_t = nullptr) noexcept {}
        ILampArrayBlinkEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArrayBlinkEffectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayBlinkEffectFactory>
    {
        ILampArrayBlinkEffectFactory(std::nullptr_t = nullptr) noexcept {}
        ILampArrayBlinkEffectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArrayColorRampEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayColorRampEffect>
    {
        ILampArrayColorRampEffect(std::nullptr_t = nullptr) noexcept {}
        ILampArrayColorRampEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArrayColorRampEffectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayColorRampEffectFactory>
    {
        ILampArrayColorRampEffectFactory(std::nullptr_t = nullptr) noexcept {}
        ILampArrayColorRampEffectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArrayCustomEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayCustomEffect>
    {
        ILampArrayCustomEffect(std::nullptr_t = nullptr) noexcept {}
        ILampArrayCustomEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArrayCustomEffectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayCustomEffectFactory>
    {
        ILampArrayCustomEffectFactory(std::nullptr_t = nullptr) noexcept {}
        ILampArrayCustomEffectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArrayEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayEffect>
    {
        ILampArrayEffect(std::nullptr_t = nullptr) noexcept {}
        ILampArrayEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArrayEffectPlaylist :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayEffectPlaylist>
    {
        ILampArrayEffectPlaylist(std::nullptr_t = nullptr) noexcept {}
        ILampArrayEffectPlaylist(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArrayEffectPlaylistStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayEffectPlaylistStatics>
    {
        ILampArrayEffectPlaylistStatics(std::nullptr_t = nullptr) noexcept {}
        ILampArrayEffectPlaylistStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArraySolidEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArraySolidEffect>
    {
        ILampArraySolidEffect(std::nullptr_t = nullptr) noexcept {}
        ILampArraySolidEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArraySolidEffectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArraySolidEffectFactory>
    {
        ILampArraySolidEffectFactory(std::nullptr_t = nullptr) noexcept {}
        ILampArraySolidEffectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILampArrayUpdateRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayUpdateRequestedEventArgs>
    {
        ILampArrayUpdateRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ILampArrayUpdateRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
