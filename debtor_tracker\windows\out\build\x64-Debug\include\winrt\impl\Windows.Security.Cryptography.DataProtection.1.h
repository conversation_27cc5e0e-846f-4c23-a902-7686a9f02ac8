// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Security_Cryptography_DataProtection_1_H
#define WINRT_Windows_Security_Cryptography_DataProtection_1_H
#include "winrt/impl/Windows.Security.Cryptography.DataProtection.0.h"
WINRT_EXPORT namespace winrt::Windows::Security::Cryptography::DataProtection
{
    struct __declspec(empty_bases) IDataProtectionProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataProtectionProvider>
    {
        IDataProtectionProvider(std::nullptr_t = nullptr) noexcept {}
        IDataProtectionProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDataProtectionProviderFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataProtectionProviderFactory>
    {
        IDataProtectionProviderFactory(std::nullptr_t = nullptr) noexcept {}
        IDataProtectionProviderFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
