{"artifacts": [{"path": "plugins/printing/printing_plugin.dll"}, {"path": "plugins/printing/printing_plugin.lib"}, {"path": "plugins/printing/printing_plugin.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_dependencies", "target_compile_options", "apply_standard_settings", "target_compile_definitions", "add_definitions", "target_compile_features"], "files": ["flutter/ephemeral/.plugin_symlinks/printing/windows/CMakeLists.txt", "flutter/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 47, "parent": 0}, {"command": 1, "file": 0, "line": 60, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 40, "parent": 3}, {"command": 4, "file": 0, "line": 55, "parent": 0}, {"command": 3, "file": 2, "line": 42, "parent": 5}, {"command": 3, "file": 2, "line": 43, "parent": 5}, {"command": 5, "file": 0, "line": 57, "parent": 0}, {"file": 2}, {"command": 6, "file": 2, "line": 33, "parent": 9}, {"command": 5, "file": 2, "line": 45, "parent": 5}, {"command": 5, "file": 2, "line": 44, "parent": 5}, {"command": 7, "file": 2, "line": 41, "parent": 5}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1"}, {"backtrace": 6, "fragment": "/W4"}, {"backtrace": 6, "fragment": "/WX"}, {"backtrace": 6, "fragment": "/wd\\\"4100\\\""}, {"backtrace": 7, "fragment": "/EHsc"}, {"fragment": "-std:c++17"}], "defines": [{"backtrace": 8, "define": "FLUTTER_PLUGIN_IMPL"}, {"backtrace": 10, "define": "UNICODE"}, {"backtrace": 11, "define": "_DEBUG"}, {"backtrace": 12, "define": "_HAS_EXCEPTIONS=0"}, {"backtrace": 10, "define": "_UNICODE"}, {"define": "printing_plugin_EXPORTS"}], "includes": [{"backtrace": 2, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral"}, {"backtrace": 2, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/cpp_client_wrapper/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/out/build/x64-Debug/pdfium-src/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/out/build/x64-Debug/pdfium-src/include/cpp"}], "language": "CXX", "languageStandard": {"backtraces": [13], "standard": "17"}, "sourceIndexes": [0, 2, 4]}], "dependencies": [{"backtrace": 4, "id": "flutter_assemble::@2bb09fbb03a373b2cb34"}, {"backtrace": 2, "id": "flutter_wrapper_plugin::@2bb09fbb03a373b2cb34"}], "id": "printing_plugin::@3f1d3ea8e3fd99684a01", "link": {"commandFragments": [{"fragment": "/machine:x64 /debug /INCREMENTAL", "role": "flags"}, {"backtrace": 2, "fragment": "..\\..\\pdfium-src\\lib\\pdfium.dll.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "..\\..\\flutter\\flutter_wrapper_plugin.lib", "role": "libraries"}, {"fragment": "..\\..\\..\\..\\..\\flutter\\ephemeral\\flutter_windows.dll.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "printing_plugin", "nameOnDisk": "printing_plugin.dll", "paths": {"build": "plugins/printing", "source": "flutter/ephemeral/.plugin_symlinks/printing/windows"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 5]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/printing/windows/printing.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/printing/windows/printing.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/printing/windows/printing_plugin.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/printing/windows/include/printing/printing_plugin.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/printing/windows/print_job.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/printing/windows/print_job.h", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}