// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_WebUI_0_H
#define WINRT_Windows_UI_WebUI_0_H
WINRT_EXPORT namespace winrt::Windows::ApplicationModel
{
    struct IEnteredBackgroundEventArgs;
    struct ILeavingBackgroundEventArgs;
    struct ISuspendingDeferral;
    struct ISuspendingEventArgs;
    struct ISuspendingOperation;
}
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Activation
{
    struct IActivatedEventArgs;
    struct IAppointmentsProviderAddAppointmentActivatedEventArgs;
    struct IAppointmentsProviderRemoveAppointmentActivatedEventArgs;
    struct IAppointmentsProviderReplaceAppointmentActivatedEventArgs;
    struct IAppointmentsProviderShowAppointmentDetailsActivatedEventArgs;
    struct IAppointmentsProviderShowTimeFrameActivatedEventArgs;
    struct IBackgroundActivatedEventArgs;
    struct IBarcodeScannerPreviewActivatedEventArgs;
    struct ICachedFileUpdaterActivatedEventArgs;
    struct ICommandLineActivatedEventArgs;
    struct IContactPanelActivatedEventArgs;
    struct IDeviceActivatedEventArgs;
    struct IDevicePairingActivatedEventArgs;
    struct IDialReceiverActivatedEventArgs;
    struct IFileActivatedEventArgs;
    struct IFileOpenPickerActivatedEventArgs;
    struct IFileOpenPickerContinuationEventArgs;
    struct IFileSavePickerActivatedEventArgs;
    struct IFileSavePickerContinuationEventArgs;
    struct IFolderPickerContinuationEventArgs;
    struct ILaunchActivatedEventArgs;
    struct ILockScreenActivatedEventArgs;
    struct IPhoneCallActivatedEventArgs;
    struct IProtocolActivatedEventArgs;
    struct IProtocolForResultsActivatedEventArgs;
    struct IRestrictedLaunchActivatedEventArgs;
    struct IShareTargetActivatedEventArgs;
    struct IStartupTaskActivatedEventArgs;
    struct IToastNotificationActivatedEventArgs;
    struct IUserDataAccountProviderActivatedEventArgs;
    struct IVoiceCommandActivatedEventArgs;
    struct IWebAccountProviderActivatedEventArgs;
    struct IWebAuthenticationBrokerContinuationEventArgs;
}
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Core
{
    enum class AppRestartFailureReason : int32_t;
}
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct Deferral;
    template <typename T> struct __declspec(empty_bases) EventHandler;
    struct EventRegistrationToken;
    template <typename TResult> struct __declspec(empty_bases) IAsyncOperation;
    template <typename TSender, typename TResult> struct __declspec(empty_bases) TypedEventHandler;
    struct Uri;
}
WINRT_EXPORT namespace winrt::Windows::System
{
    struct User;
}
WINRT_EXPORT namespace winrt::Windows::UI::WebUI
{
    enum class PrintContent : int32_t
    {
        AllPages = 0,
        CurrentPage = 1,
        CustomPageRange = 2,
        CurrentSelection = 3,
    };
    struct IActivatedDeferral;
    struct IActivatedEventArgsDeferral;
    struct IActivatedOperation;
    struct IHtmlPrintDocumentSource;
    struct INewWebUIViewCreatedEventArgs;
    struct IWebUIActivationStatics;
    struct IWebUIActivationStatics2;
    struct IWebUIActivationStatics3;
    struct IWebUIActivationStatics4;
    struct IWebUIBackgroundTaskInstance;
    struct IWebUIBackgroundTaskInstanceStatics;
    struct IWebUINavigatedDeferral;
    struct IWebUINavigatedEventArgs;
    struct IWebUINavigatedOperation;
    struct IWebUIView;
    struct IWebUIViewStatics;
    struct ActivatedDeferral;
    struct ActivatedOperation;
    struct BackgroundActivatedEventArgs;
    struct EnteredBackgroundEventArgs;
    struct HtmlPrintDocumentSource;
    struct LeavingBackgroundEventArgs;
    struct NewWebUIViewCreatedEventArgs;
    struct SuspendingDeferral;
    struct SuspendingEventArgs;
    struct SuspendingOperation;
    struct WebUIApplication;
    struct WebUIAppointmentsProviderAddAppointmentActivatedEventArgs;
    struct WebUIAppointmentsProviderRemoveAppointmentActivatedEventArgs;
    struct WebUIAppointmentsProviderReplaceAppointmentActivatedEventArgs;
    struct WebUIAppointmentsProviderShowAppointmentDetailsActivatedEventArgs;
    struct WebUIAppointmentsProviderShowTimeFrameActivatedEventArgs;
    struct WebUIBackgroundTaskInstance;
    struct WebUIBackgroundTaskInstanceRuntimeClass;
    struct WebUIBarcodeScannerPreviewActivatedEventArgs;
    struct WebUICachedFileUpdaterActivatedEventArgs;
    struct WebUICommandLineActivatedEventArgs;
    struct WebUIContactPanelActivatedEventArgs;
    struct WebUIDeviceActivatedEventArgs;
    struct WebUIDevicePairingActivatedEventArgs;
    struct WebUIDialReceiverActivatedEventArgs;
    struct WebUIFileActivatedEventArgs;
    struct WebUIFileOpenPickerActivatedEventArgs;
    struct WebUIFileOpenPickerContinuationEventArgs;
    struct WebUIFileSavePickerActivatedEventArgs;
    struct WebUIFileSavePickerContinuationEventArgs;
    struct WebUIFolderPickerContinuationEventArgs;
    struct WebUILaunchActivatedEventArgs;
    struct WebUILockScreenActivatedEventArgs;
    struct WebUILockScreenComponentActivatedEventArgs;
    struct WebUINavigatedDeferral;
    struct WebUINavigatedEventArgs;
    struct WebUINavigatedOperation;
    struct WebUIPhoneCallActivatedEventArgs;
    struct WebUIPrintWorkflowForegroundTaskActivatedEventArgs;
    struct WebUIProtocolActivatedEventArgs;
    struct WebUIProtocolForResultsActivatedEventArgs;
    struct WebUIRestrictedLaunchActivatedEventArgs;
    struct WebUIShareTargetActivatedEventArgs;
    struct WebUIStartupTaskActivatedEventArgs;
    struct WebUIToastNotificationActivatedEventArgs;
    struct WebUIUserDataAccountProviderActivatedEventArgs;
    struct WebUIView;
    struct WebUIVoiceCommandActivatedEventArgs;
    struct WebUIWebAccountProviderActivatedEventArgs;
    struct WebUIWebAuthenticationBrokerContinuationEventArgs;
    struct ActivatedEventHandler;
    struct BackgroundActivatedEventHandler;
    struct EnteredBackgroundEventHandler;
    struct LeavingBackgroundEventHandler;
    struct NavigatedEventHandler;
    struct ResumingEventHandler;
    struct SuspendingEventHandler;
}
namespace winrt::impl
{
    template <> struct category<winrt::Windows::UI::WebUI::IActivatedDeferral>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IActivatedEventArgsDeferral>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IActivatedOperation>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IHtmlPrintDocumentSource>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::INewWebUIViewCreatedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IWebUIActivationStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IWebUIActivationStatics2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IWebUIActivationStatics3>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IWebUIActivationStatics4>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IWebUIBackgroundTaskInstance>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IWebUIBackgroundTaskInstanceStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IWebUINavigatedDeferral>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IWebUINavigatedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IWebUINavigatedOperation>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IWebUIView>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::IWebUIViewStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::UI::WebUI::ActivatedDeferral>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::ActivatedOperation>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::BackgroundActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::EnteredBackgroundEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::HtmlPrintDocumentSource>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::LeavingBackgroundEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::NewWebUIViewCreatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::SuspendingDeferral>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::SuspendingEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::SuspendingOperation>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIApplication>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderAddAppointmentActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderRemoveAppointmentActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderReplaceAppointmentActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderShowAppointmentDetailsActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderShowTimeFrameActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIBackgroundTaskInstance>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIBackgroundTaskInstanceRuntimeClass>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIBarcodeScannerPreviewActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUICachedFileUpdaterActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUICommandLineActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIContactPanelActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIDeviceActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIDevicePairingActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIDialReceiverActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIFileActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIFileOpenPickerActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIFileOpenPickerContinuationEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIFileSavePickerActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIFileSavePickerContinuationEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIFolderPickerContinuationEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUILaunchActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUILockScreenActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUILockScreenComponentActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUINavigatedDeferral>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUINavigatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUINavigatedOperation>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIPhoneCallActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIPrintWorkflowForegroundTaskActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIProtocolActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIProtocolForResultsActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIRestrictedLaunchActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIShareTargetActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIStartupTaskActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIToastNotificationActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIUserDataAccountProviderActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIView>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIVoiceCommandActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIWebAccountProviderActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::WebUIWebAuthenticationBrokerContinuationEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Windows::UI::WebUI::PrintContent>{ using type = enum_category; };
    template <> struct category<winrt::Windows::UI::WebUI::ActivatedEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Windows::UI::WebUI::BackgroundActivatedEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Windows::UI::WebUI::EnteredBackgroundEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Windows::UI::WebUI::LeavingBackgroundEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Windows::UI::WebUI::NavigatedEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Windows::UI::WebUI::ResumingEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Windows::UI::WebUI::SuspendingEventHandler>{ using type = delegate_category; };
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::ActivatedDeferral> = L"Windows.UI.WebUI.ActivatedDeferral";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::ActivatedOperation> = L"Windows.UI.WebUI.ActivatedOperation";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::BackgroundActivatedEventArgs> = L"Windows.UI.WebUI.BackgroundActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::EnteredBackgroundEventArgs> = L"Windows.UI.WebUI.EnteredBackgroundEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::HtmlPrintDocumentSource> = L"Windows.UI.WebUI.HtmlPrintDocumentSource";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::LeavingBackgroundEventArgs> = L"Windows.UI.WebUI.LeavingBackgroundEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::NewWebUIViewCreatedEventArgs> = L"Windows.UI.WebUI.NewWebUIViewCreatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::SuspendingDeferral> = L"Windows.UI.WebUI.SuspendingDeferral";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::SuspendingEventArgs> = L"Windows.UI.WebUI.SuspendingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::SuspendingOperation> = L"Windows.UI.WebUI.SuspendingOperation";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIApplication> = L"Windows.UI.WebUI.WebUIApplication";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderAddAppointmentActivatedEventArgs> = L"Windows.UI.WebUI.WebUIAppointmentsProviderAddAppointmentActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderRemoveAppointmentActivatedEventArgs> = L"Windows.UI.WebUI.WebUIAppointmentsProviderRemoveAppointmentActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderReplaceAppointmentActivatedEventArgs> = L"Windows.UI.WebUI.WebUIAppointmentsProviderReplaceAppointmentActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderShowAppointmentDetailsActivatedEventArgs> = L"Windows.UI.WebUI.WebUIAppointmentsProviderShowAppointmentDetailsActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderShowTimeFrameActivatedEventArgs> = L"Windows.UI.WebUI.WebUIAppointmentsProviderShowTimeFrameActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIBackgroundTaskInstance> = L"Windows.UI.WebUI.WebUIBackgroundTaskInstance";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIBackgroundTaskInstanceRuntimeClass> = L"Windows.UI.WebUI.WebUIBackgroundTaskInstanceRuntimeClass";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIBarcodeScannerPreviewActivatedEventArgs> = L"Windows.UI.WebUI.WebUIBarcodeScannerPreviewActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUICachedFileUpdaterActivatedEventArgs> = L"Windows.UI.WebUI.WebUICachedFileUpdaterActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUICommandLineActivatedEventArgs> = L"Windows.UI.WebUI.WebUICommandLineActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIContactPanelActivatedEventArgs> = L"Windows.UI.WebUI.WebUIContactPanelActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIDeviceActivatedEventArgs> = L"Windows.UI.WebUI.WebUIDeviceActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIDevicePairingActivatedEventArgs> = L"Windows.UI.WebUI.WebUIDevicePairingActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIDialReceiverActivatedEventArgs> = L"Windows.UI.WebUI.WebUIDialReceiverActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIFileActivatedEventArgs> = L"Windows.UI.WebUI.WebUIFileActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIFileOpenPickerActivatedEventArgs> = L"Windows.UI.WebUI.WebUIFileOpenPickerActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIFileOpenPickerContinuationEventArgs> = L"Windows.UI.WebUI.WebUIFileOpenPickerContinuationEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIFileSavePickerActivatedEventArgs> = L"Windows.UI.WebUI.WebUIFileSavePickerActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIFileSavePickerContinuationEventArgs> = L"Windows.UI.WebUI.WebUIFileSavePickerContinuationEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIFolderPickerContinuationEventArgs> = L"Windows.UI.WebUI.WebUIFolderPickerContinuationEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUILaunchActivatedEventArgs> = L"Windows.UI.WebUI.WebUILaunchActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUILockScreenActivatedEventArgs> = L"Windows.UI.WebUI.WebUILockScreenActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUILockScreenComponentActivatedEventArgs> = L"Windows.UI.WebUI.WebUILockScreenComponentActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUINavigatedDeferral> = L"Windows.UI.WebUI.WebUINavigatedDeferral";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUINavigatedEventArgs> = L"Windows.UI.WebUI.WebUINavigatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUINavigatedOperation> = L"Windows.UI.WebUI.WebUINavigatedOperation";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIPhoneCallActivatedEventArgs> = L"Windows.UI.WebUI.WebUIPhoneCallActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIPrintWorkflowForegroundTaskActivatedEventArgs> = L"Windows.UI.WebUI.WebUIPrintWorkflowForegroundTaskActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIProtocolActivatedEventArgs> = L"Windows.UI.WebUI.WebUIProtocolActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIProtocolForResultsActivatedEventArgs> = L"Windows.UI.WebUI.WebUIProtocolForResultsActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIRestrictedLaunchActivatedEventArgs> = L"Windows.UI.WebUI.WebUIRestrictedLaunchActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIShareTargetActivatedEventArgs> = L"Windows.UI.WebUI.WebUIShareTargetActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIStartupTaskActivatedEventArgs> = L"Windows.UI.WebUI.WebUIStartupTaskActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIToastNotificationActivatedEventArgs> = L"Windows.UI.WebUI.WebUIToastNotificationActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIUserDataAccountProviderActivatedEventArgs> = L"Windows.UI.WebUI.WebUIUserDataAccountProviderActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIView> = L"Windows.UI.WebUI.WebUIView";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIVoiceCommandActivatedEventArgs> = L"Windows.UI.WebUI.WebUIVoiceCommandActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIWebAccountProviderActivatedEventArgs> = L"Windows.UI.WebUI.WebUIWebAccountProviderActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::WebUIWebAuthenticationBrokerContinuationEventArgs> = L"Windows.UI.WebUI.WebUIWebAuthenticationBrokerContinuationEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::PrintContent> = L"Windows.UI.WebUI.PrintContent";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IActivatedDeferral> = L"Windows.UI.WebUI.IActivatedDeferral";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IActivatedEventArgsDeferral> = L"Windows.UI.WebUI.IActivatedEventArgsDeferral";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IActivatedOperation> = L"Windows.UI.WebUI.IActivatedOperation";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IHtmlPrintDocumentSource> = L"Windows.UI.WebUI.IHtmlPrintDocumentSource";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::INewWebUIViewCreatedEventArgs> = L"Windows.UI.WebUI.INewWebUIViewCreatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IWebUIActivationStatics> = L"Windows.UI.WebUI.IWebUIActivationStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IWebUIActivationStatics2> = L"Windows.UI.WebUI.IWebUIActivationStatics2";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IWebUIActivationStatics3> = L"Windows.UI.WebUI.IWebUIActivationStatics3";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IWebUIActivationStatics4> = L"Windows.UI.WebUI.IWebUIActivationStatics4";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IWebUIBackgroundTaskInstance> = L"Windows.UI.WebUI.IWebUIBackgroundTaskInstance";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IWebUIBackgroundTaskInstanceStatics> = L"Windows.UI.WebUI.IWebUIBackgroundTaskInstanceStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IWebUINavigatedDeferral> = L"Windows.UI.WebUI.IWebUINavigatedDeferral";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IWebUINavigatedEventArgs> = L"Windows.UI.WebUI.IWebUINavigatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IWebUINavigatedOperation> = L"Windows.UI.WebUI.IWebUINavigatedOperation";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IWebUIView> = L"Windows.UI.WebUI.IWebUIView";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::IWebUIViewStatics> = L"Windows.UI.WebUI.IWebUIViewStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::ActivatedEventHandler> = L"Windows.UI.WebUI.ActivatedEventHandler";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::BackgroundActivatedEventHandler> = L"Windows.UI.WebUI.BackgroundActivatedEventHandler";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::EnteredBackgroundEventHandler> = L"Windows.UI.WebUI.EnteredBackgroundEventHandler";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::LeavingBackgroundEventHandler> = L"Windows.UI.WebUI.LeavingBackgroundEventHandler";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::NavigatedEventHandler> = L"Windows.UI.WebUI.NavigatedEventHandler";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::ResumingEventHandler> = L"Windows.UI.WebUI.ResumingEventHandler";
    template <> inline constexpr auto& name_v<winrt::Windows::UI::WebUI::SuspendingEventHandler> = L"Windows.UI.WebUI.SuspendingEventHandler";
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IActivatedDeferral>{ 0xC3BD1978,0xA431,0x49D8,{ 0xA7,0x6A,0x39,0x5A,0x4E,0x03,0xDC,0xF3 } }; // C3BD1978-A431-49D8-A76A-395A4E03DCF3
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IActivatedEventArgsDeferral>{ 0xCA6D5F74,0x63C2,0x44A6,{ 0xB9,0x7B,0xD9,0xA0,0x3C,0x20,0xBC,0x9B } }; // CA6D5F74-63C2-44A6-B97B-D9A03C20BC9B
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IActivatedOperation>{ 0xB6A0B4BC,0xC6CA,0x42FD,{ 0x98,0x18,0x71,0x90,0x4E,0x45,0xFE,0xD7 } }; // B6A0B4BC-C6CA-42FD-9818-71904E45FED7
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IHtmlPrintDocumentSource>{ 0xCEA6469A,0x0E05,0x467A,{ 0xAB,0xC9,0x36,0xEC,0x1D,0x4C,0xDC,0xB6 } }; // CEA6469A-0E05-467A-ABC9-36EC1D4CDCB6
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::INewWebUIViewCreatedEventArgs>{ 0xE8E1B216,0xBE2B,0x4C9E,{ 0x85,0xE7,0x08,0x31,0x43,0xEC,0x4B,0xE7 } }; // E8E1B216-BE2B-4C9E-85E7-083143EC4BE7
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IWebUIActivationStatics>{ 0x351B86BD,0x43B3,0x482B,{ 0x85,0xDB,0x35,0xD8,0x7B,0x51,0x7A,0xD9 } }; // 351B86BD-43B3-482B-85DB-35D87B517AD9
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IWebUIActivationStatics2>{ 0xC8E88696,0x4D78,0x4AA4,{ 0x8F,0x06,0x2A,0x9E,0xAD,0xC6,0xC4,0x0A } }; // C8E88696-4D78-4AA4-8F06-2A9EADC6C40A
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IWebUIActivationStatics3>{ 0x91ABB686,0x1AF5,0x4445,{ 0xB4,0x9F,0x94,0x59,0xF4,0x0F,0xC8,0xDE } }; // 91ABB686-1AF5-4445-B49F-9459F40FC8DE
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IWebUIActivationStatics4>{ 0x5E391429,0x183F,0x478D,{ 0x8A,0x25,0x67,0xF8,0x0D,0x03,0x93,0x5B } }; // 5E391429-183F-478D-8A25-67F80D03935B
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IWebUIBackgroundTaskInstance>{ 0x23F12C25,0xE2F7,0x4741,{ 0xBC,0x9C,0x39,0x45,0x95,0xDE,0x24,0xDC } }; // 23F12C25-E2F7-4741-BC9C-394595DE24DC
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IWebUIBackgroundTaskInstanceStatics>{ 0x9C7A5291,0x19AE,0x4CA3,{ 0xB9,0x4B,0xFE,0x4E,0xC7,0x44,0xA7,0x40 } }; // 9C7A5291-19AE-4CA3-B94B-FE4EC744A740
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IWebUINavigatedDeferral>{ 0xD804204D,0x831F,0x46E2,{ 0xB4,0x32,0x3A,0xFC,0xE2,0x11,0xF9,0x62 } }; // D804204D-831F-46E2-B432-3AFCE211F962
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IWebUINavigatedEventArgs>{ 0xA75841B8,0x2499,0x4030,{ 0xA6,0x9D,0x15,0xD2,0xD9,0xCF,0xE5,0x24 } }; // A75841B8-2499-4030-A69D-15D2D9CFE524
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IWebUINavigatedOperation>{ 0x7A965F08,0x8182,0x4A89,{ 0xAB,0x67,0x84,0x92,0xE8,0x75,0x0D,0x4B } }; // 7A965F08-8182-4A89-AB67-8492E8750D4B
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IWebUIView>{ 0x6783F64F,0x52DA,0x4FD7,{ 0xBE,0x69,0x8E,0xF6,0x28,0x4B,0x42,0x3C } }; // 6783F64F-52DA-4FD7-BE69-8EF6284B423C
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::IWebUIViewStatics>{ 0xB591E668,0x8E59,0x44F9,{ 0x88,0x03,0x1B,0x24,0xC9,0x14,0x9D,0x30 } }; // B591E668-8E59-44F9-8803-1B24C9149D30
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::ActivatedEventHandler>{ 0x50F1E730,0xC5D1,0x4B6B,{ 0x9A,0xDB,0x8A,0x11,0x75,0x6B,0xE2,0x9C } }; // 50F1E730-C5D1-4B6B-9ADB-8A11756BE29C
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::BackgroundActivatedEventHandler>{ 0xEDB19FBB,0x0761,0x47CC,{ 0x9A,0x77,0x24,0xD7,0x07,0x29,0x65,0xCA } }; // EDB19FBB-0761-47CC-9A77-24D7072965CA
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::EnteredBackgroundEventHandler>{ 0x2B09A173,0xB68E,0x4DEF,{ 0x88,0xC1,0x8D,0xE8,0x4E,0x5A,0xAB,0x2F } }; // 2B09A173-B68E-4DEF-88C1-8DE84E5AAB2F
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::LeavingBackgroundEventHandler>{ 0x00B4CCD9,0x7A9C,0x4B6B,{ 0x9A,0xC4,0x13,0x47,0x4F,0x26,0x8B,0xC4 } }; // 00B4CCD9-7A9C-4B6B-9AC4-13474F268BC4
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::NavigatedEventHandler>{ 0x7AF46FE6,0x40CA,0x4E49,{ 0xA7,0xD6,0xDB,0xDB,0x33,0x0C,0xD1,0xA3 } }; // 7AF46FE6-40CA-4E49-A7D6-DBDB330CD1A3
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::ResumingEventHandler>{ 0x26599BA9,0xA22D,0x4806,{ 0xA7,0x28,0xAC,0xAD,0xC1,0xD0,0x75,0xFA } }; // 26599BA9-A22D-4806-A728-ACADC1D075FA
    template <> inline constexpr guid guid_v<winrt::Windows::UI::WebUI::SuspendingEventHandler>{ 0x509C429C,0x78E2,0x4883,{ 0xAB,0xC8,0x89,0x60,0xDC,0xDE,0x1B,0x5C } }; // 509C429C-78E2-4883-ABC8-8960DCDE1B5C
    template <> struct default_interface<winrt::Windows::UI::WebUI::ActivatedDeferral>{ using type = winrt::Windows::UI::WebUI::IActivatedDeferral; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::ActivatedOperation>{ using type = winrt::Windows::UI::WebUI::IActivatedOperation; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::BackgroundActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IBackgroundActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::EnteredBackgroundEventArgs>{ using type = winrt::Windows::ApplicationModel::IEnteredBackgroundEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::HtmlPrintDocumentSource>{ using type = winrt::Windows::UI::WebUI::IHtmlPrintDocumentSource; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::LeavingBackgroundEventArgs>{ using type = winrt::Windows::ApplicationModel::ILeavingBackgroundEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::NewWebUIViewCreatedEventArgs>{ using type = winrt::Windows::UI::WebUI::INewWebUIViewCreatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::SuspendingDeferral>{ using type = winrt::Windows::ApplicationModel::ISuspendingDeferral; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::SuspendingEventArgs>{ using type = winrt::Windows::ApplicationModel::ISuspendingEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::SuspendingOperation>{ using type = winrt::Windows::ApplicationModel::ISuspendingOperation; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderAddAppointmentActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IAppointmentsProviderAddAppointmentActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderRemoveAppointmentActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IAppointmentsProviderRemoveAppointmentActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderReplaceAppointmentActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IAppointmentsProviderReplaceAppointmentActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderShowAppointmentDetailsActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IAppointmentsProviderShowAppointmentDetailsActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIAppointmentsProviderShowTimeFrameActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IAppointmentsProviderShowTimeFrameActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIBackgroundTaskInstanceRuntimeClass>{ using type = winrt::Windows::UI::WebUI::IWebUIBackgroundTaskInstance; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIBarcodeScannerPreviewActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IBarcodeScannerPreviewActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUICachedFileUpdaterActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::ICachedFileUpdaterActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUICommandLineActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::ICommandLineActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIContactPanelActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IContactPanelActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIDeviceActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IDeviceActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIDevicePairingActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IDevicePairingActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIDialReceiverActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IDialReceiverActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIFileActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IFileActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIFileOpenPickerActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IFileOpenPickerActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIFileOpenPickerContinuationEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IFileOpenPickerContinuationEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIFileSavePickerActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IFileSavePickerActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIFileSavePickerContinuationEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IFileSavePickerContinuationEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIFolderPickerContinuationEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IFolderPickerContinuationEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUILaunchActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::ILaunchActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUILockScreenActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::ILockScreenActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUILockScreenComponentActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUINavigatedDeferral>{ using type = winrt::Windows::UI::WebUI::IWebUINavigatedDeferral; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUINavigatedEventArgs>{ using type = winrt::Windows::UI::WebUI::IWebUINavigatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUINavigatedOperation>{ using type = winrt::Windows::UI::WebUI::IWebUINavigatedOperation; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIPhoneCallActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IPhoneCallActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIPrintWorkflowForegroundTaskActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIProtocolActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IProtocolActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIProtocolForResultsActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IProtocolForResultsActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIRestrictedLaunchActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IRestrictedLaunchActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIShareTargetActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IShareTargetActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIStartupTaskActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IStartupTaskActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIToastNotificationActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IToastNotificationActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIUserDataAccountProviderActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IUserDataAccountProviderActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIView>{ using type = winrt::Windows::UI::WebUI::IWebUIView; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIVoiceCommandActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IVoiceCommandActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIWebAccountProviderActivatedEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IWebAccountProviderActivatedEventArgs; };
    template <> struct default_interface<winrt::Windows::UI::WebUI::WebUIWebAuthenticationBrokerContinuationEventArgs>{ using type = winrt::Windows::ApplicationModel::Activation::IWebAuthenticationBrokerContinuationEventArgs; };
    template <> struct abi<winrt::Windows::UI::WebUI::IActivatedDeferral>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall Complete() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IActivatedEventArgsDeferral>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_ActivatedOperation(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IActivatedOperation>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IHtmlPrintDocumentSource>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Content(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Content(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_LeftMargin(float*) noexcept = 0;
            virtual int32_t __stdcall put_LeftMargin(float) noexcept = 0;
            virtual int32_t __stdcall get_TopMargin(float*) noexcept = 0;
            virtual int32_t __stdcall put_TopMargin(float) noexcept = 0;
            virtual int32_t __stdcall get_RightMargin(float*) noexcept = 0;
            virtual int32_t __stdcall put_RightMargin(float) noexcept = 0;
            virtual int32_t __stdcall get_BottomMargin(float*) noexcept = 0;
            virtual int32_t __stdcall put_BottomMargin(float) noexcept = 0;
            virtual int32_t __stdcall get_EnableHeaderFooter(bool*) noexcept = 0;
            virtual int32_t __stdcall put_EnableHeaderFooter(bool) noexcept = 0;
            virtual int32_t __stdcall get_ShrinkToFit(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ShrinkToFit(bool) noexcept = 0;
            virtual int32_t __stdcall get_PercentScale(float*) noexcept = 0;
            virtual int32_t __stdcall put_PercentScale(float) noexcept = 0;
            virtual int32_t __stdcall get_PageRange(void**) noexcept = 0;
            virtual int32_t __stdcall TrySetPageRange(void*, bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::INewWebUIViewCreatedEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_WebUIView(void**) noexcept = 0;
            virtual int32_t __stdcall get_ActivatedEventArgs(void**) noexcept = 0;
            virtual int32_t __stdcall get_HasPendingNavigate(bool*) noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IWebUIActivationStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall add_Activated(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Activated(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Suspending(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Suspending(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Resuming(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Resuming(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Navigated(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Navigated(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IWebUIActivationStatics2>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall add_LeavingBackground(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_LeavingBackground(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_EnteredBackground(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_EnteredBackground(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall EnablePrelaunch(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IWebUIActivationStatics3>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall RequestRestartAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall RequestRestartForUserAsync(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IWebUIActivationStatics4>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall add_NewWebUIViewCreated(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_NewWebUIViewCreated(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_BackgroundActivated(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_BackgroundActivated(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IWebUIBackgroundTaskInstance>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Succeeded(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Succeeded(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IWebUIBackgroundTaskInstanceStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_Current(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IWebUINavigatedDeferral>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall Complete() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IWebUINavigatedEventArgs>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_NavigatedOperation(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IWebUINavigatedOperation>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IWebUIView>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall get_ApplicationViewId(int32_t*) noexcept = 0;
            virtual int32_t __stdcall add_Closed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Closed(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Activated(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Activated(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall get_IgnoreApplicationContentUriRulesNavigationRestrictions(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IgnoreApplicationContentUriRulesNavigationRestrictions(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::IWebUIViewStatics>
    {
        struct __declspec(novtable) type : inspectable_abi
        {
            virtual int32_t __stdcall CreateAsync(void**) noexcept = 0;
            virtual int32_t __stdcall CreateWithUriAsync(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::ActivatedEventHandler>
    {
        struct __declspec(novtable) type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::BackgroundActivatedEventHandler>
    {
        struct __declspec(novtable) type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::EnteredBackgroundEventHandler>
    {
        struct __declspec(novtable) type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::LeavingBackgroundEventHandler>
    {
        struct __declspec(novtable) type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::NavigatedEventHandler>
    {
        struct __declspec(novtable) type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::ResumingEventHandler>
    {
        struct __declspec(novtable) type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::UI::WebUI::SuspendingEventHandler>
    {
        struct __declspec(novtable) type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IActivatedDeferral
    {
        WINRT_IMPL_AUTO(void) Complete() const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IActivatedDeferral>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IActivatedDeferral<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IActivatedEventArgsDeferral
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::UI::WebUI::ActivatedOperation) ActivatedOperation() const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IActivatedEventArgsDeferral>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IActivatedEventArgsDeferral<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IActivatedOperation
    {
        WINRT_IMPL_AUTO(winrt::Windows::UI::WebUI::ActivatedDeferral) GetDeferral() const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IActivatedOperation>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IActivatedOperation<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IHtmlPrintDocumentSource
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::UI::WebUI::PrintContent) Content() const;
        WINRT_IMPL_AUTO(void) Content(winrt::Windows::UI::WebUI::PrintContent const& value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(float) LeftMargin() const;
        WINRT_IMPL_AUTO(void) LeftMargin(float value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(float) TopMargin() const;
        WINRT_IMPL_AUTO(void) TopMargin(float value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(float) RightMargin() const;
        WINRT_IMPL_AUTO(void) RightMargin(float value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(float) BottomMargin() const;
        WINRT_IMPL_AUTO(void) BottomMargin(float value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) EnableHeaderFooter() const;
        WINRT_IMPL_AUTO(void) EnableHeaderFooter(bool value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) ShrinkToFit() const;
        WINRT_IMPL_AUTO(void) ShrinkToFit(bool value) const;
        [[nodiscard]] WINRT_IMPL_AUTO(float) PercentScale() const;
        WINRT_IMPL_AUTO(void) PercentScale(float scalePercent) const;
        [[nodiscard]] WINRT_IMPL_AUTO(hstring) PageRange() const;
        WINRT_IMPL_AUTO(bool) TrySetPageRange(param::hstring const& strPageRange) const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IHtmlPrintDocumentSource>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IHtmlPrintDocumentSource<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_INewWebUIViewCreatedEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::UI::WebUI::WebUIView) WebUIView() const;
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::ApplicationModel::Activation::IActivatedEventArgs) ActivatedEventArgs() const;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) HasPendingNavigate() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::Deferral) GetDeferral() const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::INewWebUIViewCreatedEventArgs>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_INewWebUIViewCreatedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IWebUIActivationStatics
    {
        WINRT_IMPL_AUTO(winrt::event_token) Activated(winrt::Windows::UI::WebUI::ActivatedEventHandler const& handler) const;
        using Activated_revoker = impl::event_revoker<winrt::Windows::UI::WebUI::IWebUIActivationStatics, &impl::abi_t<winrt::Windows::UI::WebUI::IWebUIActivationStatics>::remove_Activated>;
        [[nodiscard]] Activated_revoker Activated(auto_revoke_t, winrt::Windows::UI::WebUI::ActivatedEventHandler const& handler) const;
        WINRT_IMPL_AUTO(void) Activated(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) Suspending(winrt::Windows::UI::WebUI::SuspendingEventHandler const& handler) const;
        using Suspending_revoker = impl::event_revoker<winrt::Windows::UI::WebUI::IWebUIActivationStatics, &impl::abi_t<winrt::Windows::UI::WebUI::IWebUIActivationStatics>::remove_Suspending>;
        [[nodiscard]] Suspending_revoker Suspending(auto_revoke_t, winrt::Windows::UI::WebUI::SuspendingEventHandler const& handler) const;
        WINRT_IMPL_AUTO(void) Suspending(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) Resuming(winrt::Windows::UI::WebUI::ResumingEventHandler const& handler) const;
        using Resuming_revoker = impl::event_revoker<winrt::Windows::UI::WebUI::IWebUIActivationStatics, &impl::abi_t<winrt::Windows::UI::WebUI::IWebUIActivationStatics>::remove_Resuming>;
        [[nodiscard]] Resuming_revoker Resuming(auto_revoke_t, winrt::Windows::UI::WebUI::ResumingEventHandler const& handler) const;
        WINRT_IMPL_AUTO(void) Resuming(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) Navigated(winrt::Windows::UI::WebUI::NavigatedEventHandler const& handler) const;
        using Navigated_revoker = impl::event_revoker<winrt::Windows::UI::WebUI::IWebUIActivationStatics, &impl::abi_t<winrt::Windows::UI::WebUI::IWebUIActivationStatics>::remove_Navigated>;
        [[nodiscard]] Navigated_revoker Navigated(auto_revoke_t, winrt::Windows::UI::WebUI::NavigatedEventHandler const& handler) const;
        WINRT_IMPL_AUTO(void) Navigated(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IWebUIActivationStatics>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IWebUIActivationStatics<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IWebUIActivationStatics2
    {
        WINRT_IMPL_AUTO(winrt::event_token) LeavingBackground(winrt::Windows::UI::WebUI::LeavingBackgroundEventHandler const& handler) const;
        using LeavingBackground_revoker = impl::event_revoker<winrt::Windows::UI::WebUI::IWebUIActivationStatics2, &impl::abi_t<winrt::Windows::UI::WebUI::IWebUIActivationStatics2>::remove_LeavingBackground>;
        [[nodiscard]] LeavingBackground_revoker LeavingBackground(auto_revoke_t, winrt::Windows::UI::WebUI::LeavingBackgroundEventHandler const& handler) const;
        WINRT_IMPL_AUTO(void) LeavingBackground(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) EnteredBackground(winrt::Windows::UI::WebUI::EnteredBackgroundEventHandler const& handler) const;
        using EnteredBackground_revoker = impl::event_revoker<winrt::Windows::UI::WebUI::IWebUIActivationStatics2, &impl::abi_t<winrt::Windows::UI::WebUI::IWebUIActivationStatics2>::remove_EnteredBackground>;
        [[nodiscard]] EnteredBackground_revoker EnteredBackground(auto_revoke_t, winrt::Windows::UI::WebUI::EnteredBackgroundEventHandler const& handler) const;
        WINRT_IMPL_AUTO(void) EnteredBackground(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(void) EnablePrelaunch(bool value) const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IWebUIActivationStatics2>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IWebUIActivationStatics2<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IWebUIActivationStatics3
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::ApplicationModel::Core::AppRestartFailureReason>) RequestRestartAsync(param::hstring const& launchArguments) const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::ApplicationModel::Core::AppRestartFailureReason>) RequestRestartForUserAsync(winrt::Windows::System::User const& user, param::hstring const& launchArguments) const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IWebUIActivationStatics3>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IWebUIActivationStatics3<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IWebUIActivationStatics4
    {
        WINRT_IMPL_AUTO(winrt::event_token) NewWebUIViewCreated(winrt::Windows::Foundation::EventHandler<winrt::Windows::UI::WebUI::NewWebUIViewCreatedEventArgs> const& handler) const;
        using NewWebUIViewCreated_revoker = impl::event_revoker<winrt::Windows::UI::WebUI::IWebUIActivationStatics4, &impl::abi_t<winrt::Windows::UI::WebUI::IWebUIActivationStatics4>::remove_NewWebUIViewCreated>;
        [[nodiscard]] NewWebUIViewCreated_revoker NewWebUIViewCreated(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Windows::UI::WebUI::NewWebUIViewCreatedEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) NewWebUIViewCreated(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) BackgroundActivated(winrt::Windows::UI::WebUI::BackgroundActivatedEventHandler const& handler) const;
        using BackgroundActivated_revoker = impl::event_revoker<winrt::Windows::UI::WebUI::IWebUIActivationStatics4, &impl::abi_t<winrt::Windows::UI::WebUI::IWebUIActivationStatics4>::remove_BackgroundActivated>;
        [[nodiscard]] BackgroundActivated_revoker BackgroundActivated(auto_revoke_t, winrt::Windows::UI::WebUI::BackgroundActivatedEventHandler const& handler) const;
        WINRT_IMPL_AUTO(void) BackgroundActivated(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IWebUIActivationStatics4>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IWebUIActivationStatics4<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IWebUIBackgroundTaskInstance
    {
        [[nodiscard]] WINRT_IMPL_AUTO(bool) Succeeded() const;
        WINRT_IMPL_AUTO(void) Succeeded(bool succeeded) const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IWebUIBackgroundTaskInstance>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IWebUIBackgroundTaskInstance<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IWebUIBackgroundTaskInstanceStatics
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::UI::WebUI::IWebUIBackgroundTaskInstance) Current() const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IWebUIBackgroundTaskInstanceStatics>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IWebUIBackgroundTaskInstanceStatics<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IWebUINavigatedDeferral
    {
        WINRT_IMPL_AUTO(void) Complete() const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IWebUINavigatedDeferral>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IWebUINavigatedDeferral<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IWebUINavigatedEventArgs
    {
        [[nodiscard]] WINRT_IMPL_AUTO(winrt::Windows::UI::WebUI::WebUINavigatedOperation) NavigatedOperation() const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IWebUINavigatedEventArgs>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IWebUINavigatedEventArgs<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IWebUINavigatedOperation
    {
        WINRT_IMPL_AUTO(winrt::Windows::UI::WebUI::WebUINavigatedDeferral) GetDeferral() const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IWebUINavigatedOperation>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IWebUINavigatedOperation<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IWebUIView
    {
        [[nodiscard]] WINRT_IMPL_AUTO(int32_t) ApplicationViewId() const;
        WINRT_IMPL_AUTO(winrt::event_token) Closed(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::UI::WebUI::WebUIView, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using Closed_revoker = impl::event_revoker<winrt::Windows::UI::WebUI::IWebUIView, &impl::abi_t<winrt::Windows::UI::WebUI::IWebUIView>::remove_Closed>;
        [[nodiscard]] Closed_revoker Closed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::UI::WebUI::WebUIView, winrt::Windows::Foundation::IInspectable> const& handler) const;
        WINRT_IMPL_AUTO(void) Closed(winrt::event_token const& token) const noexcept;
        WINRT_IMPL_AUTO(winrt::event_token) Activated(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::UI::WebUI::WebUIView, winrt::Windows::ApplicationModel::Activation::IActivatedEventArgs> const& handler) const;
        using Activated_revoker = impl::event_revoker<winrt::Windows::UI::WebUI::IWebUIView, &impl::abi_t<winrt::Windows::UI::WebUI::IWebUIView>::remove_Activated>;
        [[nodiscard]] Activated_revoker Activated(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::UI::WebUI::WebUIView, winrt::Windows::ApplicationModel::Activation::IActivatedEventArgs> const& handler) const;
        WINRT_IMPL_AUTO(void) Activated(winrt::event_token const& token) const noexcept;
        [[nodiscard]] WINRT_IMPL_AUTO(bool) IgnoreApplicationContentUriRulesNavigationRestrictions() const;
        WINRT_IMPL_AUTO(void) IgnoreApplicationContentUriRulesNavigationRestrictions(bool value) const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IWebUIView>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IWebUIView<D>;
    };
    template <typename D>
    struct consume_Windows_UI_WebUI_IWebUIViewStatics
    {
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::UI::WebUI::WebUIView>) CreateAsync() const;
        WINRT_IMPL_AUTO(winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::UI::WebUI::WebUIView>) CreateAsync(winrt::Windows::Foundation::Uri const& uri) const;
    };
    template <> struct consume<winrt::Windows::UI::WebUI::IWebUIViewStatics>
    {
        template <typename D> using type = consume_Windows_UI_WebUI_IWebUIViewStatics<D>;
    };
}
#endif
