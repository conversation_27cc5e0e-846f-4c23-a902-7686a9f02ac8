import 'dart:convert';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

class GoogleDriveService {
  final _googleSignIn = GoogleSignIn(scopes: [drive.DriveApi.driveFileScope]);

  // دالة لتسجيل الدخول
  Future<GoogleSignInAccount?> signIn() async {
    if (await _googleSignIn.isSignedIn()) {
      return _googleSignIn.currentUser;
    }
    return await _googleSignIn.signIn();
  }

  // دالة للحصول على عميل HTTP مع صلاحيات الوصول
  Future<http.Client?> _getAuthenticatedClient() async {
    final googleUser = await signIn();
    if (googleUser == null) {
      debugPrint('فشل تسجيل الدخول');
      return null;
    }

    final headers = await googleUser.authHeaders;
    final client = authenticatedClient(
      http.Client(),
      AccessCredentials(
        AccessToken(
          'Bearer',
          headers['Authorization']!.substring(7),
          DateTime.now().toUtc().add(const Duration(hours: 1)),
        ),
        null,
        _googleSignIn.scopes,
      ),
    );
    return client;
  }

  // دالة لرفع ملف النسخة الاحتياطية
  Future<String?> uploadBackup(String backupJson) async {
    final client = await _getAuthenticatedClient();
    if (client == null) return null;

    final driveApi = drive.DriveApi(client);
    final appFolderId = await _getOrCreateAppFolder(driveApi);

    if (appFolderId == null) {
      debugPrint('لم يتم العثور على مجلد التطبيق أو إنشاؤه');
      return null;
    }

    final fileName = 'debtor_backup_${DateTime.now().toIso8601String()}.json';
    final media = drive.Media(
      Stream.value(utf8.encode(backupJson)),
      backupJson.length,
      contentType: 'application/json',
    );

    final fileToUpload =
        drive.File()
          ..name = fileName
          ..parents = [appFolderId];

    try {
      final response = await driveApi.files.create(
        fileToUpload,
        uploadMedia: media,
      );
      debugPrint('تم رفع الملف بنجاح. ID: ${response.id}');
      return response.id;
    } catch (e) {
      debugPrint('حدث خطأ أثناء رفع الملف: $e');
      return null;
    } finally {
      client.close();
    }
  }

  // دالة للبحث عن مجلد التطبيق أو إنشائه إذا لم يكن موجوداً
  Future<String?> _getOrCreateAppFolder(drive.DriveApi driveApi) async {
    const folderName = 'DebtorTrackerAppBackups';
    try {
      // البحث عن المجلد
      final response = await driveApi.files.list(
        q: "mimeType='application/vnd.google-apps.folder' and name='$folderName' and trashed=false",
        $fields: 'files(id, name)',
      );

      if (response.files != null && response.files!.isNotEmpty) {
        debugPrint('تم العثور على مجلد التطبيق: ${response.files!.first.id}');
        return response.files!.first.id;
      }

      // إذا لم يتم العثور عليه، قم بإنشائه
      debugPrint('مجلد التطبيق غير موجود، سيتم إنشاؤه...');
      final folder =
          drive.File()
            ..name = folderName
            ..mimeType = 'application/vnd.google-apps.folder';

      final createdFolder = await driveApi.files.create(folder);
      debugPrint('تم إنشاء مجلد التطبيق بنجاح: ${createdFolder.id}');
      return createdFolder.id;
    } catch (e) {
      debugPrint('حدث خطأ أثناء البحث عن المجلد أو إنشائه: $e');
      return null;
    }
  }

  // يمكنك إضافة دوال أخرى هنا مثل listBackups() و restoreBackup(fileId)
}
