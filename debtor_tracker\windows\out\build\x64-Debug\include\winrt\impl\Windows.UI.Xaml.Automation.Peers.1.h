// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Automation_Peers_1_H
#define WINRT_Windows_UI_Xaml_Automation_Peers_1_H
#include "winrt/impl/Windows.UI.Xaml.Automation.Peers.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Automation::Peers
{
    struct __declspec(empty_bases) IAppBarAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarAutomationPeer>
    {
        IAppBarAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IAppBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarAutomationPeerFactory>
    {
        IAppBarAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IAppBarAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarButtonAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarButtonAutomationPeer>
    {
        IAppBarButtonAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IAppBarButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarButtonAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarButtonAutomationPeerFactory>
    {
        IAppBarButtonAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IAppBarButtonAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarToggleButtonAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarToggleButtonAutomationPeer>
    {
        IAppBarToggleButtonAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IAppBarToggleButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAppBarToggleButtonAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppBarToggleButtonAutomationPeerFactory>
    {
        IAppBarToggleButtonAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IAppBarToggleButtonAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBoxAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBoxAutomationPeer>
    {
        IAutoSuggestBoxAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutoSuggestBoxAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBoxAutomationPeerFactory>
    {
        IAutoSuggestBoxAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBoxAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeer>
    {
        IAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeer2>
    {
        IAutomationPeer2(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeer3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeer3>
    {
        IAutomationPeer3(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeer3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeer4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeer4>
    {
        IAutomationPeer4(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeer4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeer5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeer5>
    {
        IAutomationPeer5(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeer5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeer6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeer6>
    {
        IAutomationPeer6(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeer6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeer7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeer7>
    {
        IAutomationPeer7(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeer7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeer8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeer8>
    {
        IAutomationPeer8(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeer8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeer9 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeer9>
    {
        IAutomationPeer9(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeer9(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerAnnotation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerAnnotation>
    {
        IAutomationPeerAnnotation(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerAnnotation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerAnnotationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerAnnotationFactory>
    {
        IAutomationPeerAnnotationFactory(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerAnnotationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerAnnotationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerAnnotationStatics>
    {
        IAutomationPeerAnnotationStatics(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerAnnotationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerFactory>
    {
        IAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerOverrides>
    {
        IAutomationPeerOverrides(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerOverrides2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerOverrides2>
    {
        IAutomationPeerOverrides2(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerOverrides2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerOverrides3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerOverrides3>
    {
        IAutomationPeerOverrides3(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerOverrides3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerOverrides4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerOverrides4>
    {
        IAutomationPeerOverrides4(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerOverrides4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerOverrides5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerOverrides5>
    {
        IAutomationPeerOverrides5(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerOverrides5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerOverrides6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerOverrides6>
    {
        IAutomationPeerOverrides6(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerOverrides6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerOverrides8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerOverrides8>
    {
        IAutomationPeerOverrides8(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerOverrides8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerOverrides9 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerOverrides9>
    {
        IAutomationPeerOverrides9(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerOverrides9(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerProtected :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerProtected>
    {
        IAutomationPeerProtected(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerProtected(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerStatics>
    {
        IAutomationPeerStatics(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAutomationPeerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutomationPeerStatics3>
    {
        IAutomationPeerStatics3(std::nullptr_t = nullptr) noexcept {}
        IAutomationPeerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IButtonAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IButtonAutomationPeer>
    {
        IButtonAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IButtonAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IButtonAutomationPeerFactory>
    {
        IButtonAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IButtonAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IButtonBaseAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IButtonBaseAutomationPeer>
    {
        IButtonBaseAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IButtonBaseAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IButtonBaseAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IButtonBaseAutomationPeerFactory>
    {
        IButtonBaseAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IButtonBaseAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarDatePickerAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarDatePickerAutomationPeer>
    {
        ICalendarDatePickerAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ICalendarDatePickerAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICalendarDatePickerAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICalendarDatePickerAutomationPeerFactory>
    {
        ICalendarDatePickerAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ICalendarDatePickerAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICaptureElementAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICaptureElementAutomationPeer>
    {
        ICaptureElementAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ICaptureElementAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICaptureElementAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICaptureElementAutomationPeerFactory>
    {
        ICaptureElementAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ICaptureElementAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICheckBoxAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICheckBoxAutomationPeer>
    {
        ICheckBoxAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ICheckBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICheckBoxAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICheckBoxAutomationPeerFactory>
    {
        ICheckBoxAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ICheckBoxAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorPickerSliderAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPickerSliderAutomationPeer>
    {
        IColorPickerSliderAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IColorPickerSliderAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorPickerSliderAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPickerSliderAutomationPeerFactory>
    {
        IColorPickerSliderAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IColorPickerSliderAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorSpectrumAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorSpectrumAutomationPeer>
    {
        IColorSpectrumAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IColorSpectrumAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IColorSpectrumAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorSpectrumAutomationPeerFactory>
    {
        IColorSpectrumAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IColorSpectrumAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxAutomationPeer>
    {
        IComboBoxAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IComboBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxAutomationPeerFactory>
    {
        IComboBoxAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IComboBoxAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxItemAutomationPeer>
    {
        IComboBoxItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IComboBoxItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxItemAutomationPeerFactory>
    {
        IComboBoxItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IComboBoxItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxItemDataAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxItemDataAutomationPeer>
    {
        IComboBoxItemDataAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IComboBoxItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IComboBoxItemDataAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxItemDataAutomationPeerFactory>
    {
        IComboBoxItemDataAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IComboBoxItemDataAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerAutomationPeer>
    {
        IDatePickerAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IDatePickerAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerAutomationPeerFactory>
    {
        IDatePickerAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IDatePickerAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDatePickerFlyoutPresenterAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDatePickerFlyoutPresenterAutomationPeer>
    {
        IDatePickerFlyoutPresenterAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IDatePickerFlyoutPresenterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlipViewAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlipViewAutomationPeer>
    {
        IFlipViewAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IFlipViewAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlipViewAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlipViewAutomationPeerFactory>
    {
        IFlipViewAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IFlipViewAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlipViewItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlipViewItemAutomationPeer>
    {
        IFlipViewItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IFlipViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlipViewItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlipViewItemAutomationPeerFactory>
    {
        IFlipViewItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IFlipViewItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlipViewItemDataAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlipViewItemDataAutomationPeer>
    {
        IFlipViewItemDataAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IFlipViewItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlipViewItemDataAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlipViewItemDataAutomationPeerFactory>
    {
        IFlipViewItemDataAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IFlipViewItemDataAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutPresenterAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutPresenterAutomationPeer>
    {
        IFlyoutPresenterAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IFlyoutPresenterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFlyoutPresenterAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFlyoutPresenterAutomationPeerFactory>
    {
        IFlyoutPresenterAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IFlyoutPresenterAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrameworkElementAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElementAutomationPeer>
    {
        IFrameworkElementAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElementAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrameworkElementAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElementAutomationPeerFactory>
    {
        IFrameworkElementAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElementAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFrameworkElementAutomationPeerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFrameworkElementAutomationPeerStatics>
    {
        IFrameworkElementAutomationPeerStatics(std::nullptr_t = nullptr) noexcept {}
        IFrameworkElementAutomationPeerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewAutomationPeer>
    {
        IGridViewAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IGridViewAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewAutomationPeerFactory>
    {
        IGridViewAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IGridViewAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewHeaderItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewHeaderItemAutomationPeer>
    {
        IGridViewHeaderItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IGridViewHeaderItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewHeaderItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewHeaderItemAutomationPeerFactory>
    {
        IGridViewHeaderItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IGridViewHeaderItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewItemAutomationPeer>
    {
        IGridViewItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IGridViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewItemAutomationPeerFactory>
    {
        IGridViewItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IGridViewItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewItemDataAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewItemDataAutomationPeer>
    {
        IGridViewItemDataAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IGridViewItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridViewItemDataAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridViewItemDataAutomationPeerFactory>
    {
        IGridViewItemDataAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IGridViewItemDataAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGroupItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGroupItemAutomationPeer>
    {
        IGroupItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IGroupItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGroupItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGroupItemAutomationPeerFactory>
    {
        IGroupItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IGroupItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHubAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHubAutomationPeer>
    {
        IHubAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IHubAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHubAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHubAutomationPeerFactory>
    {
        IHubAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IHubAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHubSectionAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHubSectionAutomationPeer>
    {
        IHubSectionAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IHubSectionAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHubSectionAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHubSectionAutomationPeerFactory>
    {
        IHubSectionAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IHubSectionAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlinkButtonAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlinkButtonAutomationPeer>
    {
        IHyperlinkButtonAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IHyperlinkButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IHyperlinkButtonAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHyperlinkButtonAutomationPeerFactory>
    {
        IHyperlinkButtonAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IHyperlinkButtonAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImageAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImageAutomationPeer>
    {
        IImageAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IImageAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImageAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImageAutomationPeerFactory>
    {
        IImageAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IImageAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInkToolbarAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInkToolbarAutomationPeer>
    {
        IInkToolbarAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IInkToolbarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemAutomationPeer>
    {
        IItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemAutomationPeerFactory>
    {
        IItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsControlAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsControlAutomationPeer>
    {
        IItemsControlAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IItemsControlAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsControlAutomationPeer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsControlAutomationPeer2>
    {
        IItemsControlAutomationPeer2(std::nullptr_t = nullptr) noexcept {}
        IItemsControlAutomationPeer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsControlAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsControlAutomationPeerFactory>
    {
        IItemsControlAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IItemsControlAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemsControlAutomationPeerOverrides2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemsControlAutomationPeerOverrides2>
    {
        IItemsControlAutomationPeerOverrides2(std::nullptr_t = nullptr) noexcept {}
        IItemsControlAutomationPeerOverrides2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListBoxAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListBoxAutomationPeer>
    {
        IListBoxAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IListBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListBoxAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListBoxAutomationPeerFactory>
    {
        IListBoxAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IListBoxAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListBoxItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListBoxItemAutomationPeer>
    {
        IListBoxItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IListBoxItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListBoxItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListBoxItemAutomationPeerFactory>
    {
        IListBoxItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IListBoxItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListBoxItemDataAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListBoxItemDataAutomationPeer>
    {
        IListBoxItemDataAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IListBoxItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListBoxItemDataAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListBoxItemDataAutomationPeerFactory>
    {
        IListBoxItemDataAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IListBoxItemDataAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListPickerFlyoutPresenterAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListPickerFlyoutPresenterAutomationPeer>
    {
        IListPickerFlyoutPresenterAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IListPickerFlyoutPresenterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewAutomationPeer>
    {
        IListViewAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IListViewAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewAutomationPeerFactory>
    {
        IListViewAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IListViewAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBaseAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBaseAutomationPeer>
    {
        IListViewBaseAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IListViewBaseAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBaseAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBaseAutomationPeerFactory>
    {
        IListViewBaseAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IListViewBaseAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBaseHeaderItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBaseHeaderItemAutomationPeer>
    {
        IListViewBaseHeaderItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IListViewBaseHeaderItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewBaseHeaderItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewBaseHeaderItemAutomationPeerFactory>
    {
        IListViewBaseHeaderItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IListViewBaseHeaderItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewHeaderItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewHeaderItemAutomationPeer>
    {
        IListViewHeaderItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IListViewHeaderItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewHeaderItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewHeaderItemAutomationPeerFactory>
    {
        IListViewHeaderItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IListViewHeaderItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemAutomationPeer>
    {
        IListViewItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IListViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemAutomationPeerFactory>
    {
        IListViewItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IListViewItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemDataAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemDataAutomationPeer>
    {
        IListViewItemDataAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IListViewItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IListViewItemDataAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IListViewItemDataAutomationPeerFactory>
    {
        IListViewItemDataAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IListViewItemDataAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILoopingSelectorAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILoopingSelectorAutomationPeer>
    {
        ILoopingSelectorAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ILoopingSelectorAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILoopingSelectorItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILoopingSelectorItemAutomationPeer>
    {
        ILoopingSelectorItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ILoopingSelectorItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILoopingSelectorItemDataAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILoopingSelectorItemDataAutomationPeer>
    {
        ILoopingSelectorItemDataAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ILoopingSelectorItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapControlAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapControlAutomationPeer>
    {
        IMapControlAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IMapControlAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaElementAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaElementAutomationPeer>
    {
        IMediaElementAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IMediaElementAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaElementAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaElementAutomationPeerFactory>
    {
        IMediaElementAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IMediaElementAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaPlayerElementAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaPlayerElementAutomationPeer>
    {
        IMediaPlayerElementAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IMediaPlayerElementAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaPlayerElementAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaPlayerElementAutomationPeerFactory>
    {
        IMediaPlayerElementAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IMediaPlayerElementAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControlsAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControlsAutomationPeer>
    {
        IMediaTransportControlsAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControlsAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControlsAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControlsAutomationPeerFactory>
    {
        IMediaTransportControlsAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControlsAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuBarAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarAutomationPeer>
    {
        IMenuBarAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IMenuBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuBarAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarAutomationPeerFactory>
    {
        IMenuBarAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuBarAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuBarItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarItemAutomationPeer>
    {
        IMenuBarItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IMenuBarItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuBarItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarItemAutomationPeerFactory>
    {
        IMenuBarItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuBarItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutItemAutomationPeer>
    {
        IMenuFlyoutItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutItemAutomationPeerFactory>
    {
        IMenuFlyoutItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutPresenterAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutPresenterAutomationPeer>
    {
        IMenuFlyoutPresenterAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutPresenterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMenuFlyoutPresenterAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuFlyoutPresenterAutomationPeerFactory>
    {
        IMenuFlyoutPresenterAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuFlyoutPresenterAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemAutomationPeer>
    {
        INavigationViewItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) INavigationViewItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemAutomationPeerFactory>
    {
        INavigationViewItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPasswordBoxAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPasswordBoxAutomationPeer>
    {
        IPasswordBoxAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IPasswordBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPasswordBoxAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPasswordBoxAutomationPeerFactory>
    {
        IPasswordBoxAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IPasswordBoxAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPersonPictureAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPersonPictureAutomationPeer>
    {
        IPersonPictureAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IPersonPictureAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPersonPictureAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPersonPictureAutomationPeerFactory>
    {
        IPersonPictureAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IPersonPictureAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPickerFlyoutPresenterAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPickerFlyoutPresenterAutomationPeer>
    {
        IPickerFlyoutPresenterAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IPickerFlyoutPresenterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotAutomationPeer>
    {
        IPivotAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IPivotAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotAutomationPeerFactory>
    {
        IPivotAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IPivotAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotItemAutomationPeer>
    {
        IPivotItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IPivotItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotItemAutomationPeerFactory>
    {
        IPivotItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IPivotItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotItemDataAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotItemDataAutomationPeer>
    {
        IPivotItemDataAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IPivotItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPivotItemDataAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPivotItemDataAutomationPeerFactory>
    {
        IPivotItemDataAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IPivotItemDataAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProgressBarAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressBarAutomationPeer>
    {
        IProgressBarAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IProgressBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProgressBarAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressBarAutomationPeerFactory>
    {
        IProgressBarAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IProgressBarAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProgressRingAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressRingAutomationPeer>
    {
        IProgressRingAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IProgressRingAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProgressRingAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressRingAutomationPeerFactory>
    {
        IProgressRingAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IProgressRingAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRadioButtonAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadioButtonAutomationPeer>
    {
        IRadioButtonAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IRadioButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRadioButtonAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadioButtonAutomationPeerFactory>
    {
        IRadioButtonAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IRadioButtonAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRangeBaseAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRangeBaseAutomationPeer>
    {
        IRangeBaseAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IRangeBaseAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRangeBaseAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRangeBaseAutomationPeerFactory>
    {
        IRangeBaseAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IRangeBaseAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRatingControlAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingControlAutomationPeer>
    {
        IRatingControlAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IRatingControlAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRatingControlAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingControlAutomationPeerFactory>
    {
        IRatingControlAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IRatingControlAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRepeatButtonAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRepeatButtonAutomationPeer>
    {
        IRepeatButtonAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IRepeatButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRepeatButtonAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRepeatButtonAutomationPeerFactory>
    {
        IRepeatButtonAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IRepeatButtonAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxAutomationPeer>
    {
        IRichEditBoxAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichEditBoxAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichEditBoxAutomationPeerFactory>
    {
        IRichEditBoxAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IRichEditBoxAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockAutomationPeer>
    {
        IRichTextBlockAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockAutomationPeerFactory>
    {
        IRichTextBlockAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockOverflowAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockOverflowAutomationPeer>
    {
        IRichTextBlockOverflowAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockOverflowAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRichTextBlockOverflowAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRichTextBlockOverflowAutomationPeerFactory>
    {
        IRichTextBlockOverflowAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IRichTextBlockOverflowAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollBarAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollBarAutomationPeer>
    {
        IScrollBarAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IScrollBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollBarAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollBarAutomationPeerFactory>
    {
        IScrollBarAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IScrollBarAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollViewerAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollViewerAutomationPeer>
    {
        IScrollViewerAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IScrollViewerAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollViewerAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollViewerAutomationPeerFactory>
    {
        IScrollViewerAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IScrollViewerAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISearchBoxAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchBoxAutomationPeer>
    {
        ISearchBoxAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ISearchBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISearchBoxAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchBoxAutomationPeerFactory>
    {
        ISearchBoxAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ISearchBoxAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectorAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectorAutomationPeer>
    {
        ISelectorAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ISelectorAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectorAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectorAutomationPeerFactory>
    {
        ISelectorAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ISelectorAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectorItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectorItemAutomationPeer>
    {
        ISelectorItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ISelectorItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectorItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectorItemAutomationPeerFactory>
    {
        ISelectorItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ISelectorItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISemanticZoomAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISemanticZoomAutomationPeer>
    {
        ISemanticZoomAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ISemanticZoomAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISemanticZoomAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISemanticZoomAutomationPeerFactory>
    {
        ISemanticZoomAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ISemanticZoomAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISettingsFlyoutAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISettingsFlyoutAutomationPeer>
    {
        ISettingsFlyoutAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ISettingsFlyoutAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISettingsFlyoutAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISettingsFlyoutAutomationPeerFactory>
    {
        ISettingsFlyoutAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ISettingsFlyoutAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISliderAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISliderAutomationPeer>
    {
        ISliderAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ISliderAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISliderAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISliderAutomationPeerFactory>
    {
        ISliderAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ISliderAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlockAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlockAutomationPeer>
    {
        ITextBlockAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ITextBlockAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBlockAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBlockAutomationPeerFactory>
    {
        ITextBlockAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ITextBlockAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxAutomationPeer>
    {
        ITextBoxAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ITextBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextBoxAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextBoxAutomationPeerFactory>
    {
        ITextBoxAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ITextBoxAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IThumbAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IThumbAutomationPeer>
    {
        IThumbAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IThumbAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IThumbAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IThumbAutomationPeerFactory>
    {
        IThumbAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IThumbAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerAutomationPeer>
    {
        ITimePickerAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ITimePickerAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerAutomationPeerFactory>
    {
        ITimePickerAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ITimePickerAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimePickerFlyoutPresenterAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimePickerFlyoutPresenterAutomationPeer>
    {
        ITimePickerFlyoutPresenterAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ITimePickerFlyoutPresenterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleButtonAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleButtonAutomationPeer>
    {
        IToggleButtonAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IToggleButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleButtonAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleButtonAutomationPeerFactory>
    {
        IToggleButtonAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IToggleButtonAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleMenuFlyoutItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleMenuFlyoutItemAutomationPeer>
    {
        IToggleMenuFlyoutItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IToggleMenuFlyoutItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleMenuFlyoutItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleMenuFlyoutItemAutomationPeerFactory>
    {
        IToggleMenuFlyoutItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IToggleMenuFlyoutItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleSwitchAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleSwitchAutomationPeer>
    {
        IToggleSwitchAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IToggleSwitchAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleSwitchAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleSwitchAutomationPeerFactory>
    {
        IToggleSwitchAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IToggleSwitchAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItemAutomationPeer>
    {
        ITreeViewItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItemAutomationPeerFactory>
    {
        ITreeViewItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewListAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewListAutomationPeer>
    {
        ITreeViewListAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ITreeViewListAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITreeViewListAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewListAutomationPeerFactory>
    {
        ITreeViewListAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ITreeViewListAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
