import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import '../utils/app_theme.dart';
import '../services/security_service.dart';
import 'main_screen.dart';
import 'dart:math' as math;

class BiometricAuthScreen extends StatefulWidget {
  const BiometricAuthScreen({super.key});

  @override
  State<BiometricAuthScreen> createState() => _BiometricAuthScreenState();
}

class _BiometricAuthScreenState extends State<BiometricAuthScreen>
    with TickerProviderStateMixin {
  final LocalAuthentication _localAuth = LocalAuthentication();
  final TextEditingController _passwordController = TextEditingController();

  late AnimationController _pulseController;
  late AnimationController _shakeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _shakeAnimation;

  bool _isAuthenticating = false;
  bool _showPasswordInput = false;
  bool _obscurePassword = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _checkBiometricAvailability();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _shakeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _shakeController, curve: Curves.elasticIn),
    );

    _pulseController.repeat(reverse: true);
  }

  Future<void> _checkBiometricAvailability() async {
    try {
      final bool isAvailable = await _localAuth.canCheckBiometrics;
      final bool isDeviceSupported = await _localAuth.isDeviceSupported();

      if (isAvailable && isDeviceSupported) {
        final List<BiometricType> availableBiometrics =
            await _localAuth.getAvailableBiometrics();

        if (availableBiometrics.isNotEmpty) {
          // Auto-trigger biometric authentication
          Future.delayed(const Duration(milliseconds: 500), () {
            _authenticateWithBiometrics();
          });
        } else {
          _showPasswordInput = true;
          setState(() {});
        }
      } else {
        _showPasswordInput = true;
        setState(() {});
      }
    } catch (e) {
      _showPasswordInput = true;
      setState(() {});
    }
  }

  Future<void> _authenticateWithBiometrics() async {
    if (_isAuthenticating) return;

    setState(() {
      _isAuthenticating = true;
      _errorMessage = '';
    });

    try {
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'يرجى التحقق من هويتك للوصول إلى التطبيق',
        options: const AuthenticationOptions(
          biometricOnly: false,
          stickyAuth: true,
        ),
      );

      if (didAuthenticate) {
        _navigateToMainScreen();
      } else {
        _showAuthenticationError('فشل في التحقق من الهوية');
      }
    } on PlatformException catch (e) {
      _handlePlatformException(e);
    } catch (e) {
      _showAuthenticationError('حدث خطأ غير متوقع');
    } finally {
      setState(() {
        _isAuthenticating = false;
      });
    }
  }

  void _handlePlatformException(PlatformException e) {
    switch (e.code) {
      case 'NotAvailable':
        setState(() {
          _showPasswordInput = true;
          _errorMessage = 'المصادقة البايومترية غير متاحة';
        });
        break;
      case 'NotEnrolled':
        setState(() {
          _showPasswordInput = true;
          _errorMessage = 'لم يتم تسجيل بيانات بايومترية';
        });
        break;
      case 'LockedOut':
        _showAuthenticationError('تم قفل المصادقة البايومترية مؤقتاً');
        break;
      case 'PermanentlyLockedOut':
        setState(() {
          _showPasswordInput = true;
          _errorMessage = 'تم قفل المصادقة البايومترية نهائياً';
        });
        break;
      default:
        _showAuthenticationError('فشل في المصادقة البايومترية');
    }
  }

  void _showAuthenticationError(String message) {
    setState(() {
      _errorMessage = message;
    });
    _shakeController.forward().then((_) {
      _shakeController.reset();
    });
  }

  Future<void> _authenticateWithPassword() async {
    if (_passwordController.text.isEmpty) {
      _showAuthenticationError('يرجى إدخال كلمة المرور');
      return;
    }

    setState(() {
      _isAuthenticating = true;
      _errorMessage = '';
    });

    try {
      final bool isValid = await SecurityService.verifyPassword(
        _passwordController.text,
      );

      if (isValid) {
        _navigateToMainScreen();
      } else {
        _showAuthenticationError('كلمة المرور غير صحيحة');
      }
    } catch (e) {
      _showAuthenticationError('حدث خطأ في التحقق من كلمة المرور');
    } finally {
      setState(() {
        _isAuthenticating = false;
      });
    }
  }

  void _navigateToMainScreen() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder:
            (context, animation, secondaryAnimation) => const MainScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 800),
      ),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _shakeController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Widget _buildBiometricSection() {
    return Column(
      children: [
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _pulseAnimation.value,
              child: GestureDetector(
                onTap: _isAuthenticating ? null : _authenticateWithBiometrics,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(60),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.5),
                      width: 2,
                    ),
                  ),
                  child:
                      _isAuthenticating
                          ? const CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          )
                          : const Icon(
                            Icons.fingerprint,
                            size: 60,
                            color: Colors.white,
                          ),
                ),
              ),
            );
          },
        ),

        const SizedBox(height: 24),

        Text(
          _isAuthenticating ? 'جاري التحقق...' : 'اضغط للمصادقة البايومترية',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.9),
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPasswordSection() {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
          ),
          child: TextField(
            controller: _passwordController,
            obscureText: _obscurePassword,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'كلمة المرور',
              hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.7)),
              prefixIcon: const Icon(Icons.lock, color: Colors.white),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscurePassword ? Icons.visibility : Icons.visibility_off,
                  color: Colors.white,
                ),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
            ),
            onSubmitted: (_) => _authenticateWithPassword(),
          ),
        ),

        const SizedBox(height: 24),

        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isAuthenticating ? null : _authenticateWithPassword,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child:
                _isAuthenticating
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppTheme.primaryColor,
                        ),
                      ),
                    )
                    : const Text(
                      'تسجيل الدخول',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.primaryColor.withValues(alpha: 0.8),
              AppTheme.primaryColor.withValues(alpha: 0.6),
              AppTheme.secondaryColor.withValues(alpha: 0.4),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App Logo
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.security,
                    size: 60,
                    color: AppTheme.primaryColor,
                  ),
                ),

                const SizedBox(height: 40),

                // Title
                Text(
                  'حماية التطبيق',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const SizedBox(height: 16),

                Text(
                  'يرجى التحقق من هويتك للمتابعة',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 60),

                if (!_showPasswordInput) ...[
                  // Biometric Authentication
                  _buildBiometricSection(),
                ] else ...[
                  // Password Authentication
                  _buildPasswordSection(),
                ],

                if (_errorMessage.isNotEmpty) ...[
                  const SizedBox(height: 20),
                  AnimatedBuilder(
                    animation: _shakeAnimation,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(
                          math.sin(_shakeAnimation.value * math.pi * 4) * 10,
                          0,
                        ),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.red.withValues(alpha: 0.5),
                            ),
                          ),
                          child: Text(
                            _errorMessage,
                            style: const TextStyle(color: Colors.white),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      );
                    },
                  ),
                ],

                const SizedBox(height: 40),

                // Switch Authentication Method
                if (!_showPasswordInput)
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _showPasswordInput = true;
                      });
                    },
                    child: Text(
                      'استخدام كلمة المرور بدلاً من ذلك',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
