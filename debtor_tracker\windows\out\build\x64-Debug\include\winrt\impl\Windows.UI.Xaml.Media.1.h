// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Media_1_H
#define WINRT_Windows_UI_Xaml_Media_1_H
#include "winrt/impl/Windows.UI.Xaml.Media.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Media
{
    struct __declspec(empty_bases) IAcrylicBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAcrylicBrush>
    {
        IAcrylicBrush(std::nullptr_t = nullptr) noexcept {}
        IAcrylicBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAcrylicBrush2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAcrylicBrush2>
    {
        IAcrylicBrush2(std::nullptr_t = nullptr) noexcept {}
        IAcrylicBrush2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAcrylicBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAcrylicBrushFactory>
    {
        IAcrylicBrushFactory(std::nullptr_t = nullptr) noexcept {}
        IAcrylicBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAcrylicBrushStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAcrylicBrushStatics>
    {
        IAcrylicBrushStatics(std::nullptr_t = nullptr) noexcept {}
        IAcrylicBrushStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAcrylicBrushStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAcrylicBrushStatics2>
    {
        IAcrylicBrushStatics2(std::nullptr_t = nullptr) noexcept {}
        IAcrylicBrushStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IArcSegment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IArcSegment>
    {
        IArcSegment(std::nullptr_t = nullptr) noexcept {}
        IArcSegment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IArcSegmentStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IArcSegmentStatics>
    {
        IArcSegmentStatics(std::nullptr_t = nullptr) noexcept {}
        IArcSegmentStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBezierSegment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBezierSegment>
    {
        IBezierSegment(std::nullptr_t = nullptr) noexcept {}
        IBezierSegment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBezierSegmentStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBezierSegmentStatics>
    {
        IBezierSegmentStatics(std::nullptr_t = nullptr) noexcept {}
        IBezierSegmentStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBitmapCache :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBitmapCache>
    {
        IBitmapCache(std::nullptr_t = nullptr) noexcept {}
        IBitmapCache(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBrush>
    {
        IBrush(std::nullptr_t = nullptr) noexcept {}
        IBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBrushFactory>
    {
        IBrushFactory(std::nullptr_t = nullptr) noexcept {}
        IBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBrushOverrides2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBrushOverrides2>
    {
        IBrushOverrides2(std::nullptr_t = nullptr) noexcept {}
        IBrushOverrides2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBrushStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBrushStatics>
    {
        IBrushStatics(std::nullptr_t = nullptr) noexcept {}
        IBrushStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICacheMode :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICacheMode>
    {
        ICacheMode(std::nullptr_t = nullptr) noexcept {}
        ICacheMode(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICacheModeFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICacheModeFactory>
    {
        ICacheModeFactory(std::nullptr_t = nullptr) noexcept {}
        ICacheModeFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositeTransform :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositeTransform>
    {
        ICompositeTransform(std::nullptr_t = nullptr) noexcept {}
        ICompositeTransform(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositeTransformStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositeTransformStatics>
    {
        ICompositeTransformStatics(std::nullptr_t = nullptr) noexcept {}
        ICompositeTransformStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionTarget :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionTarget>
    {
        ICompositionTarget(std::nullptr_t = nullptr) noexcept {}
        ICompositionTarget(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionTargetStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionTargetStatics>
    {
        ICompositionTargetStatics(std::nullptr_t = nullptr) noexcept {}
        ICompositionTargetStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICompositionTargetStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionTargetStatics3>
    {
        ICompositionTargetStatics3(std::nullptr_t = nullptr) noexcept {}
        ICompositionTargetStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IEllipseGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEllipseGeometry>
    {
        IEllipseGeometry(std::nullptr_t = nullptr) noexcept {}
        IEllipseGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IEllipseGeometryStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEllipseGeometryStatics>
    {
        IEllipseGeometryStatics(std::nullptr_t = nullptr) noexcept {}
        IEllipseGeometryStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFontFamily :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFontFamily>
    {
        IFontFamily(std::nullptr_t = nullptr) noexcept {}
        IFontFamily(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFontFamilyFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFontFamilyFactory>
    {
        IFontFamilyFactory(std::nullptr_t = nullptr) noexcept {}
        IFontFamilyFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFontFamilyStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFontFamilyStatics2>
    {
        IFontFamilyStatics2(std::nullptr_t = nullptr) noexcept {}
        IFontFamilyStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGeneralTransform :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeneralTransform>
    {
        IGeneralTransform(std::nullptr_t = nullptr) noexcept {}
        IGeneralTransform(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGeneralTransformFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeneralTransformFactory>
    {
        IGeneralTransformFactory(std::nullptr_t = nullptr) noexcept {}
        IGeneralTransformFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGeneralTransformOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeneralTransformOverrides>
    {
        IGeneralTransformOverrides(std::nullptr_t = nullptr) noexcept {}
        IGeneralTransformOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeometry>
    {
        IGeometry(std::nullptr_t = nullptr) noexcept {}
        IGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGeometryFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeometryFactory>
    {
        IGeometryFactory(std::nullptr_t = nullptr) noexcept {}
        IGeometryFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGeometryGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeometryGroup>
    {
        IGeometryGroup(std::nullptr_t = nullptr) noexcept {}
        IGeometryGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGeometryGroupStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeometryGroupStatics>
    {
        IGeometryGroupStatics(std::nullptr_t = nullptr) noexcept {}
        IGeometryGroupStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGeometryStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeometryStatics>
    {
        IGeometryStatics(std::nullptr_t = nullptr) noexcept {}
        IGeometryStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGradientBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGradientBrush>
    {
        IGradientBrush(std::nullptr_t = nullptr) noexcept {}
        IGradientBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGradientBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGradientBrushFactory>
    {
        IGradientBrushFactory(std::nullptr_t = nullptr) noexcept {}
        IGradientBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGradientBrushStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGradientBrushStatics>
    {
        IGradientBrushStatics(std::nullptr_t = nullptr) noexcept {}
        IGradientBrushStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGradientStop :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGradientStop>
    {
        IGradientStop(std::nullptr_t = nullptr) noexcept {}
        IGradientStop(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGradientStopStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGradientStopStatics>
    {
        IGradientStopStatics(std::nullptr_t = nullptr) noexcept {}
        IGradientStopStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImageBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImageBrush>
    {
        IImageBrush(std::nullptr_t = nullptr) noexcept {}
        IImageBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImageBrushStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImageBrushStatics>
    {
        IImageBrushStatics(std::nullptr_t = nullptr) noexcept {}
        IImageBrushStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImageSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImageSource>
    {
        IImageSource(std::nullptr_t = nullptr) noexcept {}
        IImageSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IImageSourceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImageSourceFactory>
    {
        IImageSourceFactory(std::nullptr_t = nullptr) noexcept {}
        IImageSourceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILineGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILineGeometry>
    {
        ILineGeometry(std::nullptr_t = nullptr) noexcept {}
        ILineGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILineGeometryStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILineGeometryStatics>
    {
        ILineGeometryStatics(std::nullptr_t = nullptr) noexcept {}
        ILineGeometryStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILineSegment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILineSegment>
    {
        ILineSegment(std::nullptr_t = nullptr) noexcept {}
        ILineSegment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILineSegmentStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILineSegmentStatics>
    {
        ILineSegmentStatics(std::nullptr_t = nullptr) noexcept {}
        ILineSegmentStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILinearGradientBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILinearGradientBrush>
    {
        ILinearGradientBrush(std::nullptr_t = nullptr) noexcept {}
        ILinearGradientBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILinearGradientBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILinearGradientBrushFactory>
    {
        ILinearGradientBrushFactory(std::nullptr_t = nullptr) noexcept {}
        ILinearGradientBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILinearGradientBrushStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILinearGradientBrushStatics>
    {
        ILinearGradientBrushStatics(std::nullptr_t = nullptr) noexcept {}
        ILinearGradientBrushStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILoadedImageSourceLoadCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILoadedImageSourceLoadCompletedEventArgs>
    {
        ILoadedImageSourceLoadCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ILoadedImageSourceLoadCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILoadedImageSurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILoadedImageSurface>
    {
        ILoadedImageSurface(std::nullptr_t = nullptr) noexcept {}
        ILoadedImageSurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILoadedImageSurfaceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILoadedImageSurfaceStatics>
    {
        ILoadedImageSurfaceStatics(std::nullptr_t = nullptr) noexcept {}
        ILoadedImageSurfaceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMatrix3DProjection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMatrix3DProjection>
    {
        IMatrix3DProjection(std::nullptr_t = nullptr) noexcept {}
        IMatrix3DProjection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMatrix3DProjectionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMatrix3DProjectionStatics>
    {
        IMatrix3DProjectionStatics(std::nullptr_t = nullptr) noexcept {}
        IMatrix3DProjectionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMatrixHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMatrixHelper>
    {
        IMatrixHelper(std::nullptr_t = nullptr) noexcept {}
        IMatrixHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMatrixHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMatrixHelperStatics>
    {
        IMatrixHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IMatrixHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMatrixTransform :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMatrixTransform>
    {
        IMatrixTransform(std::nullptr_t = nullptr) noexcept {}
        IMatrixTransform(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMatrixTransformStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMatrixTransformStatics>
    {
        IMatrixTransformStatics(std::nullptr_t = nullptr) noexcept {}
        IMatrixTransformStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMediaTransportControlsThumbnailRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaTransportControlsThumbnailRequestedEventArgs>
    {
        IMediaTransportControlsThumbnailRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IMediaTransportControlsThumbnailRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPartialMediaFailureDetectedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPartialMediaFailureDetectedEventArgs>
    {
        IPartialMediaFailureDetectedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPartialMediaFailureDetectedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPartialMediaFailureDetectedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPartialMediaFailureDetectedEventArgs2>
    {
        IPartialMediaFailureDetectedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IPartialMediaFailureDetectedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathFigure :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathFigure>
    {
        IPathFigure(std::nullptr_t = nullptr) noexcept {}
        IPathFigure(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathFigureStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathFigureStatics>
    {
        IPathFigureStatics(std::nullptr_t = nullptr) noexcept {}
        IPathFigureStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathGeometry>
    {
        IPathGeometry(std::nullptr_t = nullptr) noexcept {}
        IPathGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathGeometryStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathGeometryStatics>
    {
        IPathGeometryStatics(std::nullptr_t = nullptr) noexcept {}
        IPathGeometryStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathSegment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathSegment>
    {
        IPathSegment(std::nullptr_t = nullptr) noexcept {}
        IPathSegment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPathSegmentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathSegmentFactory>
    {
        IPathSegmentFactory(std::nullptr_t = nullptr) noexcept {}
        IPathSegmentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPlaneProjection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPlaneProjection>
    {
        IPlaneProjection(std::nullptr_t = nullptr) noexcept {}
        IPlaneProjection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPlaneProjectionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPlaneProjectionStatics>
    {
        IPlaneProjectionStatics(std::nullptr_t = nullptr) noexcept {}
        IPlaneProjectionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPolyBezierSegment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPolyBezierSegment>
    {
        IPolyBezierSegment(std::nullptr_t = nullptr) noexcept {}
        IPolyBezierSegment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPolyBezierSegmentStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPolyBezierSegmentStatics>
    {
        IPolyBezierSegmentStatics(std::nullptr_t = nullptr) noexcept {}
        IPolyBezierSegmentStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPolyLineSegment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPolyLineSegment>
    {
        IPolyLineSegment(std::nullptr_t = nullptr) noexcept {}
        IPolyLineSegment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPolyLineSegmentStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPolyLineSegmentStatics>
    {
        IPolyLineSegmentStatics(std::nullptr_t = nullptr) noexcept {}
        IPolyLineSegmentStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPolyQuadraticBezierSegment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPolyQuadraticBezierSegment>
    {
        IPolyQuadraticBezierSegment(std::nullptr_t = nullptr) noexcept {}
        IPolyQuadraticBezierSegment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPolyQuadraticBezierSegmentStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPolyQuadraticBezierSegmentStatics>
    {
        IPolyQuadraticBezierSegmentStatics(std::nullptr_t = nullptr) noexcept {}
        IPolyQuadraticBezierSegmentStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProjection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProjection>
    {
        IProjection(std::nullptr_t = nullptr) noexcept {}
        IProjection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IProjectionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProjectionFactory>
    {
        IProjectionFactory(std::nullptr_t = nullptr) noexcept {}
        IProjectionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IQuadraticBezierSegment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IQuadraticBezierSegment>
    {
        IQuadraticBezierSegment(std::nullptr_t = nullptr) noexcept {}
        IQuadraticBezierSegment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IQuadraticBezierSegmentStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IQuadraticBezierSegmentStatics>
    {
        IQuadraticBezierSegmentStatics(std::nullptr_t = nullptr) noexcept {}
        IQuadraticBezierSegmentStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRateChangedRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRateChangedRoutedEventArgs>
    {
        IRateChangedRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRateChangedRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRectangleGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRectangleGeometry>
    {
        IRectangleGeometry(std::nullptr_t = nullptr) noexcept {}
        IRectangleGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRectangleGeometryStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRectangleGeometryStatics>
    {
        IRectangleGeometryStatics(std::nullptr_t = nullptr) noexcept {}
        IRectangleGeometryStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRenderedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRenderedEventArgs>
    {
        IRenderedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRenderedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRenderingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRenderingEventArgs>
    {
        IRenderingEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRenderingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRevealBackgroundBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBackgroundBrush>
    {
        IRevealBackgroundBrush(std::nullptr_t = nullptr) noexcept {}
        IRevealBackgroundBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRevealBackgroundBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBackgroundBrushFactory>
    {
        IRevealBackgroundBrushFactory(std::nullptr_t = nullptr) noexcept {}
        IRevealBackgroundBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRevealBorderBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBorderBrush>
    {
        IRevealBorderBrush(std::nullptr_t = nullptr) noexcept {}
        IRevealBorderBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRevealBorderBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBorderBrushFactory>
    {
        IRevealBorderBrushFactory(std::nullptr_t = nullptr) noexcept {}
        IRevealBorderBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRevealBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBrush>
    {
        IRevealBrush(std::nullptr_t = nullptr) noexcept {}
        IRevealBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRevealBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBrushFactory>
    {
        IRevealBrushFactory(std::nullptr_t = nullptr) noexcept {}
        IRevealBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRevealBrushStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBrushStatics>
    {
        IRevealBrushStatics(std::nullptr_t = nullptr) noexcept {}
        IRevealBrushStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRotateTransform :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRotateTransform>
    {
        IRotateTransform(std::nullptr_t = nullptr) noexcept {}
        IRotateTransform(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRotateTransformStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRotateTransformStatics>
    {
        IRotateTransformStatics(std::nullptr_t = nullptr) noexcept {}
        IRotateTransformStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScaleTransform :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScaleTransform>
    {
        IScaleTransform(std::nullptr_t = nullptr) noexcept {}
        IScaleTransform(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScaleTransformStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScaleTransformStatics>
    {
        IScaleTransformStatics(std::nullptr_t = nullptr) noexcept {}
        IScaleTransformStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IShadow :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IShadow>
    {
        IShadow(std::nullptr_t = nullptr) noexcept {}
        IShadow(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IShadowFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IShadowFactory>
    {
        IShadowFactory(std::nullptr_t = nullptr) noexcept {}
        IShadowFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISkewTransform :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISkewTransform>
    {
        ISkewTransform(std::nullptr_t = nullptr) noexcept {}
        ISkewTransform(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISkewTransformStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISkewTransformStatics>
    {
        ISkewTransformStatics(std::nullptr_t = nullptr) noexcept {}
        ISkewTransformStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISolidColorBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISolidColorBrush>
    {
        ISolidColorBrush(std::nullptr_t = nullptr) noexcept {}
        ISolidColorBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISolidColorBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISolidColorBrushFactory>
    {
        ISolidColorBrushFactory(std::nullptr_t = nullptr) noexcept {}
        ISolidColorBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISolidColorBrushStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISolidColorBrushStatics>
    {
        ISolidColorBrushStatics(std::nullptr_t = nullptr) noexcept {}
        ISolidColorBrushStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IThemeShadow :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IThemeShadow>
    {
        IThemeShadow(std::nullptr_t = nullptr) noexcept {}
        IThemeShadow(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IThemeShadowFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IThemeShadowFactory>
    {
        IThemeShadowFactory(std::nullptr_t = nullptr) noexcept {}
        IThemeShadowFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileBrush>
    {
        ITileBrush(std::nullptr_t = nullptr) noexcept {}
        ITileBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileBrushFactory>
    {
        ITileBrushFactory(std::nullptr_t = nullptr) noexcept {}
        ITileBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITileBrushStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITileBrushStatics>
    {
        ITileBrushStatics(std::nullptr_t = nullptr) noexcept {}
        ITileBrushStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimelineMarker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimelineMarker>
    {
        ITimelineMarker(std::nullptr_t = nullptr) noexcept {}
        ITimelineMarker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimelineMarkerRoutedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimelineMarkerRoutedEventArgs>
    {
        ITimelineMarkerRoutedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ITimelineMarkerRoutedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITimelineMarkerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimelineMarkerStatics>
    {
        ITimelineMarkerStatics(std::nullptr_t = nullptr) noexcept {}
        ITimelineMarkerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITransform :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransform>
    {
        ITransform(std::nullptr_t = nullptr) noexcept {}
        ITransform(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITransformFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransformFactory>
    {
        ITransformFactory(std::nullptr_t = nullptr) noexcept {}
        ITransformFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITransformGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransformGroup>
    {
        ITransformGroup(std::nullptr_t = nullptr) noexcept {}
        ITransformGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITransformGroupStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransformGroupStatics>
    {
        ITransformGroupStatics(std::nullptr_t = nullptr) noexcept {}
        ITransformGroupStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITranslateTransform :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITranslateTransform>
    {
        ITranslateTransform(std::nullptr_t = nullptr) noexcept {}
        ITranslateTransform(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITranslateTransformStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITranslateTransformStatics>
    {
        ITranslateTransformStatics(std::nullptr_t = nullptr) noexcept {}
        ITranslateTransformStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVisualTreeHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualTreeHelper>
    {
        IVisualTreeHelper(std::nullptr_t = nullptr) noexcept {}
        IVisualTreeHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVisualTreeHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualTreeHelperStatics>
    {
        IVisualTreeHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IVisualTreeHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVisualTreeHelperStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualTreeHelperStatics2>
    {
        IVisualTreeHelperStatics2(std::nullptr_t = nullptr) noexcept {}
        IVisualTreeHelperStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVisualTreeHelperStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualTreeHelperStatics3>
    {
        IVisualTreeHelperStatics3(std::nullptr_t = nullptr) noexcept {}
        IVisualTreeHelperStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IXamlCompositionBrushBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlCompositionBrushBase>
    {
        IXamlCompositionBrushBase(std::nullptr_t = nullptr) noexcept {}
        IXamlCompositionBrushBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IXamlCompositionBrushBaseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlCompositionBrushBaseFactory>
    {
        IXamlCompositionBrushBaseFactory(std::nullptr_t = nullptr) noexcept {}
        IXamlCompositionBrushBaseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IXamlCompositionBrushBaseOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlCompositionBrushBaseOverrides>
    {
        IXamlCompositionBrushBaseOverrides(std::nullptr_t = nullptr) noexcept {}
        IXamlCompositionBrushBaseOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IXamlCompositionBrushBaseProtected :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlCompositionBrushBaseProtected>
    {
        IXamlCompositionBrushBaseProtected(std::nullptr_t = nullptr) noexcept {}
        IXamlCompositionBrushBaseProtected(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IXamlCompositionBrushBaseStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlCompositionBrushBaseStatics>
    {
        IXamlCompositionBrushBaseStatics(std::nullptr_t = nullptr) noexcept {}
        IXamlCompositionBrushBaseStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IXamlLight :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlLight>
    {
        IXamlLight(std::nullptr_t = nullptr) noexcept {}
        IXamlLight(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IXamlLightFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlLightFactory>
    {
        IXamlLightFactory(std::nullptr_t = nullptr) noexcept {}
        IXamlLightFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IXamlLightOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlLightOverrides>
    {
        IXamlLightOverrides(std::nullptr_t = nullptr) noexcept {}
        IXamlLightOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IXamlLightProtected :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlLightProtected>
    {
        IXamlLightProtected(std::nullptr_t = nullptr) noexcept {}
        IXamlLightProtected(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IXamlLightStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlLightStatics>
    {
        IXamlLightStatics(std::nullptr_t = nullptr) noexcept {}
        IXamlLightStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
