{"artifacts": [{"path": "plugins/audioplayers_windows/audioplayers_windows_plugin.dll"}, {"path": "plugins/audioplayers_windows/audioplayers_windows_plugin.lib"}, {"path": "plugins/audioplayers_windows/audioplayers_windows_plugin.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_dependencies", "target_compile_options", "apply_standard_settings", "target_compile_definitions", "add_definitions", "target_compile_features"], "files": ["flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows/CMakeLists.txt", "flutter/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 32, "parent": 0}, {"command": 1, "file": 0, "line": 48, "parent": 0}, {"command": 1, "file": 0, "line": 49, "parent": 0}, {"command": 1, "file": 0, "line": 54, "parent": 0}, {"command": 1, "file": 0, "line": 55, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 40, "parent": 6}, {"command": 4, "file": 0, "line": 44, "parent": 0}, {"command": 3, "file": 2, "line": 42, "parent": 8}, {"command": 3, "file": 2, "line": 43, "parent": 8}, {"command": 5, "file": 0, "line": 51, "parent": 0}, {"file": 2}, {"command": 6, "file": 2, "line": 33, "parent": 12}, {"command": 5, "file": 2, "line": 45, "parent": 8}, {"command": 5, "file": 2, "line": 44, "parent": 8}, {"command": 7, "file": 2, "line": 41, "parent": 8}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1"}, {"backtrace": 9, "fragment": "/W4"}, {"backtrace": 9, "fragment": "/WX"}, {"backtrace": 9, "fragment": "/wd\\\"4100\\\""}, {"backtrace": 10, "fragment": "/EHsc"}, {"fragment": "-std:c++17"}], "defines": [{"backtrace": 11, "define": "FLUTTER_PLUGIN_IMPL"}, {"backtrace": 13, "define": "UNICODE"}, {"backtrace": 14, "define": "_DEBUG"}, {"backtrace": 15, "define": "_HAS_EXCEPTIONS=0"}, {"backtrace": 13, "define": "_UNICODE"}, {"define": "audioplayers_windows_plugin_EXPORTS"}], "includes": [{"backtrace": 4, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/cpp_client_wrapper/include"}], "language": "CXX", "languageStandard": {"backtraces": [16], "standard": "17"}, "sourceIndexes": [0, 2, 6, 9]}], "dependencies": [{"backtrace": 7, "id": "flutter_assemble::@2bb09fbb03a373b2cb34"}, {"backtrace": 4, "id": "flutter_wrapper_plugin::@2bb09fbb03a373b2cb34"}], "id": "audioplayers_windows_plugin::@6f00b843ae6f76675cd2", "link": {"commandFragments": [{"fragment": "/machine:x64 /debug /INCREMENTAL", "role": "flags"}, {"backtrace": 2, "fragment": "..\\..\\packages\\Microsoft.Windows.ImplementationLibrary\\build\\native\\Microsoft.Windows.ImplementationLibrary.targets", "role": "libraries"}, {"backtrace": 3, "fragment": "Mfplat.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "windowsapp.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "..\\..\\flutter\\flutter_wrapper_plugin.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "shlwapi.lib", "role": "libraries"}, {"fragment": "..\\..\\..\\..\\..\\flutter\\ephemeral\\flutter_windows.dll.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "audioplayers_windows_plugin", "nameOnDisk": "audioplayers_windows_plugin.dll", "paths": {"build": "plugins/audioplayers_windows", "source": "flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 6, 9]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 4, 5, 7, 8]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows/audioplayers_windows_plugin.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows/audio_player.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows/audio_player.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows/audioplayers_helpers.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows/event_stream_handler.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows/MediaEngineExtension.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows/MediaEngineExtension.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows/MediaFoundationHelpers.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows/MediaEngineWrapper.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows/MediaEngineWrapper.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}