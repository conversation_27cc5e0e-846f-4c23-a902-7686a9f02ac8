import 'package:flutter/material.dart';

/// Widget wrapper that prevents bottom overflow issues
/// by automatically adding scrolling when content exceeds screen height
class OverflowSafeWrapper extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final bool enableScroll;
  final ScrollPhysics? physics;
  final Axis scrollDirection;

  const OverflowSafeWrapper({
    super.key,
    required this.child,
    this.padding,
    this.enableScroll = true,
    this.physics,
    this.scrollDirection = Axis.vertical,
  });

  @override
  Widget build(BuildContext context) {
    if (!enableScroll) {
      return Padding(
        padding: padding ?? EdgeInsets.zero,
        child: child,
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          scrollDirection: scrollDirection,
          physics: physics ?? const BouncingScrollPhysics(),
          padding: padding,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight,
            ),
            child: IntrinsicHeight(
              child: child,
            ),
          ),
        );
      },
    );
  }
}

/// Safe area wrapper that handles keyboard and system UI overlays
class SafeAreaWrapper extends StatelessWidget {
  final Widget child;
  final bool top;
  final bool bottom;
  final bool left;
  final bool right;
  final EdgeInsets? minimum;

  const SafeAreaWrapper({
    super.key,
    required this.child,
    this.top = true,
    this.bottom = true,
    this.left = true,
    this.right = true,
    this.minimum,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: top,
      bottom: bottom,
      left: left,
      right: right,
      minimum: minimum ?? EdgeInsets.zero,
      child: child,
    );
  }
}

/// Keyboard aware wrapper that adjusts content when keyboard appears
class KeyboardAwareWrapper extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final bool resizeToAvoidBottomInset;

  const KeyboardAwareWrapper({
    super.key,
    required this.child,
    this.padding,
    this.resizeToAvoidBottomInset = true,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final keyboardHeight = mediaQuery.viewInsets.bottom;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      padding: EdgeInsets.only(
        top: padding?.top ?? 0,
        left: padding?.left ?? 0,
        right: padding?.right ?? 0,
        bottom: (padding?.bottom ?? 0) + 
               (resizeToAvoidBottomInset ? keyboardHeight : 0),
      ),
      child: child,
    );
  }
}

/// Complete responsive wrapper that combines all safety features
class ResponsiveWrapper extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final bool enableScroll;
  final bool enableSafeArea;
  final bool enableKeyboardAware;
  final ScrollPhysics? physics;

  const ResponsiveWrapper({
    super.key,
    required this.child,
    this.padding,
    this.enableScroll = true,
    this.enableSafeArea = true,
    this.enableKeyboardAware = true,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    Widget wrappedChild = child;

    // Apply keyboard awareness
    if (enableKeyboardAware) {
      wrappedChild = KeyboardAwareWrapper(
        padding: padding,
        child: wrappedChild,
      );
    }

    // Apply overflow safety
    if (enableScroll) {
      wrappedChild = OverflowSafeWrapper(
        padding: enableKeyboardAware ? null : padding,
        physics: physics,
        child: wrappedChild,
      );
    }

    // Apply safe area
    if (enableSafeArea) {
      wrappedChild = SafeAreaWrapper(
        child: wrappedChild,
      );
    }

    return wrappedChild;
  }
}

/// Dialog wrapper that prevents overflow in dialogs
class DialogWrapper extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final double? maxHeight;
  final double? maxWidth;

  const DialogWrapper({
    super.key,
    required this.child,
    this.padding,
    this.maxHeight,
    this.maxWidth,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: maxHeight ?? screenSize.height * 0.8,
          maxWidth: maxWidth ?? screenSize.width * 0.9,
        ),
        child: OverflowSafeWrapper(
          padding: padding ?? const EdgeInsets.all(16),
          child: child,
        ),
      ),
    );
  }
}

/// Bottom sheet wrapper that prevents overflow
class BottomSheetWrapper extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final double? maxHeight;

  const BottomSheetWrapper({
    super.key,
    required this.child,
    this.padding,
    this.maxHeight,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    
    return Container(
      constraints: BoxConstraints(
        maxHeight: maxHeight ?? screenSize.height * 0.9,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: OverflowSafeWrapper(
        padding: padding ?? const EdgeInsets.all(16),
        child: child,
      ),
    );
  }
}
