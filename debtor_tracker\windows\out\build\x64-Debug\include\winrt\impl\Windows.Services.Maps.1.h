// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Services_Maps_1_H
#define WINRT_Windows_Services_Maps_1_H
#include "winrt/impl/Windows.Services.Maps.0.h"
WINRT_EXPORT namespace winrt::Windows::Services::Maps
{
    struct __declspec(empty_bases) IEnhancedWaypoint :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEnhancedWaypoint>
    {
        IEnhancedWaypoint(std::nullptr_t = nullptr) noexcept {}
        IEnhancedWaypoint(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IEnhancedWaypointFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEnhancedWaypointFactory>
    {
        IEnhancedWaypointFactory(std::nullptr_t = nullptr) noexcept {}
        IEnhancedWaypointFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IManeuverWarning :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IManeuverWarning>
    {
        IManeuverWarning(std::nullptr_t = nullptr) noexcept {}
        IManeuverWarning(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapAddress :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapAddress>
    {
        IMapAddress(std::nullptr_t = nullptr) noexcept {}
        IMapAddress(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapAddress2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapAddress2>
    {
        IMapAddress2(std::nullptr_t = nullptr) noexcept {}
        IMapAddress2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapLocation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapLocation>
    {
        IMapLocation(std::nullptr_t = nullptr) noexcept {}
        IMapLocation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapLocationFinderResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapLocationFinderResult>
    {
        IMapLocationFinderResult(std::nullptr_t = nullptr) noexcept {}
        IMapLocationFinderResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapLocationFinderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapLocationFinderStatics>
    {
        IMapLocationFinderStatics(std::nullptr_t = nullptr) noexcept {}
        IMapLocationFinderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapLocationFinderStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapLocationFinderStatics2>
    {
        IMapLocationFinderStatics2(std::nullptr_t = nullptr) noexcept {}
        IMapLocationFinderStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapManagerStatics>
    {
        IMapManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IMapManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRoute :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRoute>
    {
        IMapRoute(std::nullptr_t = nullptr) noexcept {}
        IMapRoute(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRoute2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRoute2>
    {
        IMapRoute2(std::nullptr_t = nullptr) noexcept {}
        IMapRoute2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRoute3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRoute3>
    {
        IMapRoute3(std::nullptr_t = nullptr) noexcept {}
        IMapRoute3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRoute4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRoute4>
    {
        IMapRoute4(std::nullptr_t = nullptr) noexcept {}
        IMapRoute4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteDrivingOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteDrivingOptions>
    {
        IMapRouteDrivingOptions(std::nullptr_t = nullptr) noexcept {}
        IMapRouteDrivingOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteDrivingOptions2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteDrivingOptions2>
    {
        IMapRouteDrivingOptions2(std::nullptr_t = nullptr) noexcept {}
        IMapRouteDrivingOptions2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteFinderResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteFinderResult>
    {
        IMapRouteFinderResult(std::nullptr_t = nullptr) noexcept {}
        IMapRouteFinderResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteFinderResult2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteFinderResult2>
    {
        IMapRouteFinderResult2(std::nullptr_t = nullptr) noexcept {}
        IMapRouteFinderResult2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteFinderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteFinderStatics>
    {
        IMapRouteFinderStatics(std::nullptr_t = nullptr) noexcept {}
        IMapRouteFinderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteFinderStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteFinderStatics2>
    {
        IMapRouteFinderStatics2(std::nullptr_t = nullptr) noexcept {}
        IMapRouteFinderStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteFinderStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteFinderStatics3>
    {
        IMapRouteFinderStatics3(std::nullptr_t = nullptr) noexcept {}
        IMapRouteFinderStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteLeg :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteLeg>
    {
        IMapRouteLeg(std::nullptr_t = nullptr) noexcept {}
        IMapRouteLeg(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteLeg2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteLeg2>
    {
        IMapRouteLeg2(std::nullptr_t = nullptr) noexcept {}
        IMapRouteLeg2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteManeuver :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteManeuver>
    {
        IMapRouteManeuver(std::nullptr_t = nullptr) noexcept {}
        IMapRouteManeuver(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteManeuver2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteManeuver2>
    {
        IMapRouteManeuver2(std::nullptr_t = nullptr) noexcept {}
        IMapRouteManeuver2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapRouteManeuver3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapRouteManeuver3>
    {
        IMapRouteManeuver3(std::nullptr_t = nullptr) noexcept {}
        IMapRouteManeuver3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapServiceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapServiceStatics>
    {
        IMapServiceStatics(std::nullptr_t = nullptr) noexcept {}
        IMapServiceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapServiceStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapServiceStatics2>
    {
        IMapServiceStatics2(std::nullptr_t = nullptr) noexcept {}
        IMapServiceStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapServiceStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapServiceStatics3>
    {
        IMapServiceStatics3(std::nullptr_t = nullptr) noexcept {}
        IMapServiceStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMapServiceStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMapServiceStatics4>
    {
        IMapServiceStatics4(std::nullptr_t = nullptr) noexcept {}
        IMapServiceStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPlaceInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPlaceInfo>
    {
        IPlaceInfo(std::nullptr_t = nullptr) noexcept {}
        IPlaceInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPlaceInfoCreateOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPlaceInfoCreateOptions>
    {
        IPlaceInfoCreateOptions(std::nullptr_t = nullptr) noexcept {}
        IPlaceInfoCreateOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPlaceInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPlaceInfoStatics>
    {
        IPlaceInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IPlaceInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPlaceInfoStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPlaceInfoStatics2>
    {
        IPlaceInfoStatics2(std::nullptr_t = nullptr) noexcept {}
        IPlaceInfoStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
