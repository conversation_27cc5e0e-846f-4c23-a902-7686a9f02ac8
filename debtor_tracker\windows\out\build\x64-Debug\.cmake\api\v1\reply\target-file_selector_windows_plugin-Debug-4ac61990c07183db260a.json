{"artifacts": [{"path": "plugins/file_selector_windows/file_selector_windows_plugin.dll"}, {"path": "plugins/file_selector_windows/file_selector_windows_plugin.lib"}, {"path": "plugins/file_selector_windows/file_selector_windows_plugin.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_dependencies", "target_compile_options", "apply_standard_settings", "target_compile_definitions", "add_definitions", "target_compile_features"], "files": ["flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows/CMakeLists.txt", "flutter/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 20, "parent": 0}, {"command": 1, "file": 0, "line": 30, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 40, "parent": 3}, {"command": 4, "file": 0, "line": 25, "parent": 0}, {"command": 3, "file": 2, "line": 42, "parent": 5}, {"command": 3, "file": 2, "line": 43, "parent": 5}, {"command": 5, "file": 0, "line": 27, "parent": 0}, {"file": 2}, {"command": 6, "file": 2, "line": 33, "parent": 9}, {"command": 5, "file": 2, "line": 45, "parent": 5}, {"command": 5, "file": 2, "line": 44, "parent": 5}, {"command": 5, "file": 0, "line": 33, "parent": 0}, {"command": 7, "file": 2, "line": 41, "parent": 5}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1"}, {"backtrace": 6, "fragment": "/W4"}, {"backtrace": 6, "fragment": "/WX"}, {"backtrace": 6, "fragment": "/wd\\\"4100\\\""}, {"backtrace": 7, "fragment": "/EHsc"}, {"fragment": "-std:c++17"}], "defines": [{"backtrace": 8, "define": "FLUTTER_PLUGIN_IMPL"}, {"backtrace": 10, "define": "UNICODE"}, {"backtrace": 11, "define": "_DEBUG"}, {"backtrace": 12, "define": "_HAS_EXCEPTIONS=0"}, {"backtrace": 13, "define": "_HAS_EXCEPTIONS=1"}, {"backtrace": 10, "define": "_UNICODE"}, {"define": "file_selector_windows_plugin_EXPORTS"}], "includes": [{"backtrace": 2, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral"}, {"backtrace": 2, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/cpp_client_wrapper/include"}], "language": "CXX", "languageStandard": {"backtraces": [14], "standard": "17"}, "sourceIndexes": [0, 2, 4, 6, 8]}], "dependencies": [{"backtrace": 4, "id": "flutter_assemble::@2bb09fbb03a373b2cb34"}, {"backtrace": 2, "id": "flutter_wrapper_plugin::@2bb09fbb03a373b2cb34"}], "id": "file_selector_windows_plugin::@24329985f05541c6c489", "link": {"commandFragments": [{"fragment": "/machine:x64 /debug /INCREMENTAL", "role": "flags"}, {"backtrace": 2, "fragment": "..\\..\\flutter\\flutter_wrapper_plugin.lib", "role": "libraries"}, {"fragment": "..\\..\\..\\..\\..\\flutter\\ephemeral\\flutter_windows.dll.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "file_selector_windows_plugin", "nameOnDisk": "file_selector_windows_plugin.dll", "paths": {"build": "plugins/file_selector_windows", "source": "flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4, 6, 8]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 5, 7, 9]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows/file_selector_windows.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows/include/file_selector_windows/file_selector_windows.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows/file_dialog_controller.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows/file_dialog_controller.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows/file_selector_plugin.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows/file_selector_plugin.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows/messages.g.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows/messages.g.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows/string_utils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows/string_utils.h", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}