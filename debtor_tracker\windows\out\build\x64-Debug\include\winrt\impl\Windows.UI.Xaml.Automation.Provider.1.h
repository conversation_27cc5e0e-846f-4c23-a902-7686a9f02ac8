// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Automation_Provider_1_H
#define WINRT_Windows_UI_Xaml_Automation_Provider_1_H
#include "winrt/impl/Windows.UI.Xaml.Automation.Provider.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Automation::Provider
{
    struct __declspec(empty_bases) IAnnotationProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnnotationProvider>
    {
        IAnnotationProvider(std::nullptr_t = nullptr) noexcept {}
        IAnnotationProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ICustomNavigationProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICustomNavigationProvider>
    {
        ICustomNavigationProvider(std::nullptr_t = nullptr) noexcept {}
        ICustomNavigationProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDockProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDockProvider>
    {
        IDockProvider(std::nullptr_t = nullptr) noexcept {}
        IDockProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDragProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragProvider>
    {
        IDragProvider(std::nullptr_t = nullptr) noexcept {}
        IDragProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDropTargetProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropTargetProvider>
    {
        IDropTargetProvider(std::nullptr_t = nullptr) noexcept {}
        IDropTargetProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IExpandCollapseProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExpandCollapseProvider>
    {
        IExpandCollapseProvider(std::nullptr_t = nullptr) noexcept {}
        IExpandCollapseProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridItemProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridItemProvider>
    {
        IGridItemProvider(std::nullptr_t = nullptr) noexcept {}
        IGridItemProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGridProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGridProvider>
    {
        IGridProvider(std::nullptr_t = nullptr) noexcept {}
        IGridProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IIRawElementProviderSimple :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IIRawElementProviderSimple>
    {
        IIRawElementProviderSimple(std::nullptr_t = nullptr) noexcept {}
        IIRawElementProviderSimple(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IInvokeProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInvokeProvider>
    {
        IInvokeProvider(std::nullptr_t = nullptr) noexcept {}
        IInvokeProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IItemContainerProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IItemContainerProvider>
    {
        IItemContainerProvider(std::nullptr_t = nullptr) noexcept {}
        IItemContainerProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IMultipleViewProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMultipleViewProvider>
    {
        IMultipleViewProvider(std::nullptr_t = nullptr) noexcept {}
        IMultipleViewProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IObjectModelProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IObjectModelProvider>
    {
        IObjectModelProvider(std::nullptr_t = nullptr) noexcept {}
        IObjectModelProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRangeValueProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRangeValueProvider>
    {
        IRangeValueProvider(std::nullptr_t = nullptr) noexcept {}
        IRangeValueProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollItemProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollItemProvider>
    {
        IScrollItemProvider(std::nullptr_t = nullptr) noexcept {}
        IScrollItemProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IScrollProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScrollProvider>
    {
        IScrollProvider(std::nullptr_t = nullptr) noexcept {}
        IScrollProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectionItemProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectionItemProvider>
    {
        ISelectionItemProvider(std::nullptr_t = nullptr) noexcept {}
        ISelectionItemProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectionProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectionProvider>
    {
        ISelectionProvider(std::nullptr_t = nullptr) noexcept {}
        ISelectionProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpreadsheetItemProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpreadsheetItemProvider>
    {
        ISpreadsheetItemProvider(std::nullptr_t = nullptr) noexcept {}
        ISpreadsheetItemProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISpreadsheetProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpreadsheetProvider>
    {
        ISpreadsheetProvider(std::nullptr_t = nullptr) noexcept {}
        ISpreadsheetProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStylesProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStylesProvider>
    {
        IStylesProvider(std::nullptr_t = nullptr) noexcept {}
        IStylesProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISynchronizedInputProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISynchronizedInputProvider>
    {
        ISynchronizedInputProvider(std::nullptr_t = nullptr) noexcept {}
        ISynchronizedInputProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITableItemProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITableItemProvider>
    {
        ITableItemProvider(std::nullptr_t = nullptr) noexcept {}
        ITableItemProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITableProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITableProvider>
    {
        ITableProvider(std::nullptr_t = nullptr) noexcept {}
        ITableProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextChildProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextChildProvider>
    {
        ITextChildProvider(std::nullptr_t = nullptr) noexcept {}
        ITextChildProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextEditProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextEditProvider>,
        impl::require<winrt::Windows::UI::Xaml::Automation::Provider::ITextEditProvider, winrt::Windows::UI::Xaml::Automation::Provider::ITextProvider>
    {
        ITextEditProvider(std::nullptr_t = nullptr) noexcept {}
        ITextEditProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextProvider>
    {
        ITextProvider(std::nullptr_t = nullptr) noexcept {}
        ITextProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextProvider2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextProvider2>,
        impl::require<winrt::Windows::UI::Xaml::Automation::Provider::ITextProvider2, winrt::Windows::UI::Xaml::Automation::Provider::ITextProvider>
    {
        ITextProvider2(std::nullptr_t = nullptr) noexcept {}
        ITextProvider2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextRangeProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextRangeProvider>
    {
        ITextRangeProvider(std::nullptr_t = nullptr) noexcept {}
        ITextRangeProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextRangeProvider2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextRangeProvider2>,
        impl::require<winrt::Windows::UI::Xaml::Automation::Provider::ITextRangeProvider2, winrt::Windows::UI::Xaml::Automation::Provider::ITextRangeProvider>
    {
        ITextRangeProvider2(std::nullptr_t = nullptr) noexcept {}
        ITextRangeProvider2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IToggleProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleProvider>
    {
        IToggleProvider(std::nullptr_t = nullptr) noexcept {}
        IToggleProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITransformProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransformProvider>
    {
        ITransformProvider(std::nullptr_t = nullptr) noexcept {}
        ITransformProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITransformProvider2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransformProvider2>,
        impl::require<winrt::Windows::UI::Xaml::Automation::Provider::ITransformProvider2, winrt::Windows::UI::Xaml::Automation::Provider::ITransformProvider>
    {
        ITransformProvider2(std::nullptr_t = nullptr) noexcept {}
        ITransformProvider2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IValueProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IValueProvider>
    {
        IValueProvider(std::nullptr_t = nullptr) noexcept {}
        IValueProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IVirtualizedItemProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVirtualizedItemProvider>
    {
        IVirtualizedItemProvider(std::nullptr_t = nullptr) noexcept {}
        IVirtualizedItemProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWindowProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowProvider>
    {
        IWindowProvider(std::nullptr_t = nullptr) noexcept {}
        IWindowProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
