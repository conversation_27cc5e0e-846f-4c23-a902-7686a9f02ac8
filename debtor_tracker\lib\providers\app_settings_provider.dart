import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum AppThemeMode { light, dark, system }
enum AppLanguage { arabic, english }

class AppSettingsProvider extends ChangeNotifier {
  static const String _themeKey = 'app_theme_mode';
  static const String _languageKey = 'app_language';
  static const String _isFirstLaunchKey = 'is_first_launch';

  AppThemeMode _themeMode = AppThemeMode.system;
  AppLanguage _language = AppLanguage.arabic;
  bool _isFirstLaunch = true;
  SharedPreferences? _prefs;

  AppThemeMode get themeMode => _themeMode;
  AppLanguage get language => _language;
  bool get isFirstLaunch => _isFirstLaunch;

  Locale get locale {
    switch (_language) {
      case AppLanguage.arabic:
        return const Locale('ar', 'SA');
      case AppLanguage.english:
        return const Locale('en', 'US');
    }
  }

  ThemeMode get materialThemeMode {
    switch (_themeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  String get themeModeDisplayName {
    switch (_themeMode) {
      case AppThemeMode.light:
        return _language == AppLanguage.arabic ? 'الوضع النهاري' : 'Light Mode';
      case AppThemeMode.dark:
        return _language == AppLanguage.arabic ? 'الوضع الليلي' : 'Dark Mode';
      case AppThemeMode.system:
        return _language == AppLanguage.arabic ? 'وضع النظام' : 'System Mode';
    }
  }

  String get languageDisplayName {
    switch (_language) {
      case AppLanguage.arabic:
        return 'العربية';
      case AppLanguage.english:
        return 'English';
    }
  }

  IconData get themeModeIcon {
    switch (_themeMode) {
      case AppThemeMode.light:
        return Icons.light_mode;
      case AppThemeMode.dark:
        return Icons.dark_mode;
      case AppThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  IconData get languageIcon {
    switch (_language) {
      case AppLanguage.arabic:
        return Icons.language;
      case AppLanguage.english:
        return Icons.translate;
    }
  }

  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadSettings();
  }

  Future<void> _loadSettings() async {
    if (_prefs == null) return;

    // Load theme mode
    final themeIndex = _prefs!.getInt(_themeKey) ?? AppThemeMode.system.index;
    _themeMode = AppThemeMode.values[themeIndex];

    // Load language
    final languageIndex = _prefs!.getInt(_languageKey) ?? AppLanguage.arabic.index;
    _language = AppLanguage.values[languageIndex];

    // Load first launch status
    _isFirstLaunch = _prefs!.getBool(_isFirstLaunchKey) ?? true;

    notifyListeners();
  }

  Future<void> setThemeMode(AppThemeMode mode) async {
    if (_themeMode == mode) return;

    _themeMode = mode;
    await _prefs?.setInt(_themeKey, mode.index);
    notifyListeners();
  }

  Future<void> setLanguage(AppLanguage language) async {
    if (_language == language) return;

    _language = language;
    await _prefs?.setInt(_languageKey, language.index);
    notifyListeners();
  }

  Future<void> setFirstLaunchCompleted() async {
    _isFirstLaunch = false;
    await _prefs?.setBool(_isFirstLaunchKey, false);
    notifyListeners();
  }

  Future<void> toggleTheme() async {
    switch (_themeMode) {
      case AppThemeMode.light:
        await setThemeMode(AppThemeMode.dark);
        break;
      case AppThemeMode.dark:
        await setThemeMode(AppThemeMode.system);
        break;
      case AppThemeMode.system:
        await setThemeMode(AppThemeMode.light);
        break;
    }
  }

  Future<void> toggleLanguage() async {
    switch (_language) {
      case AppLanguage.arabic:
        await setLanguage(AppLanguage.english);
        break;
      case AppLanguage.english:
        await setLanguage(AppLanguage.arabic);
        break;
    }
  }

  // Helper methods for UI
  List<AppThemeMode> get availableThemeModes => AppThemeMode.values;
  List<AppLanguage> get availableLanguages => AppLanguage.values;

  String getThemeModeDisplayName(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.light:
        return _language == AppLanguage.arabic ? 'الوضع النهاري' : 'Light Mode';
      case AppThemeMode.dark:
        return _language == AppLanguage.arabic ? 'الوضع الليلي' : 'Dark Mode';
      case AppThemeMode.system:
        return _language == AppLanguage.arabic ? 'وضع النظام' : 'System Mode';
    }
  }

  String getLanguageDisplayName(AppLanguage language) {
    switch (language) {
      case AppLanguage.arabic:
        return 'العربية';
      case AppLanguage.english:
        return 'English';
    }
  }

  IconData getThemeModeIcon(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.light:
        return Icons.light_mode;
      case AppThemeMode.dark:
        return Icons.dark_mode;
      case AppThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  IconData getLanguageIcon(AppLanguage language) {
    switch (language) {
      case AppLanguage.arabic:
        return Icons.language;
      case AppLanguage.english:
        return Icons.translate;
    }
  }

  // Reset all settings to default
  Future<void> resetToDefaults() async {
    _themeMode = AppThemeMode.system;
    _language = AppLanguage.arabic;
    _isFirstLaunch = true;

    await _prefs?.setInt(_themeKey, _themeMode.index);
    await _prefs?.setInt(_languageKey, _language.index);
    await _prefs?.setBool(_isFirstLaunchKey, _isFirstLaunch);

    notifyListeners();
  }

  // Export settings
  Map<String, dynamic> exportSettings() {
    return {
      'themeMode': _themeMode.index,
      'language': _language.index,
      'isFirstLaunch': _isFirstLaunch,
    };
  }

  // Import settings
  Future<void> importSettings(Map<String, dynamic> settings) async {
    if (settings.containsKey('themeMode')) {
      final themeIndex = settings['themeMode'] as int;
      if (themeIndex >= 0 && themeIndex < AppThemeMode.values.length) {
        _themeMode = AppThemeMode.values[themeIndex];
        await _prefs?.setInt(_themeKey, _themeMode.index);
      }
    }

    if (settings.containsKey('language')) {
      final languageIndex = settings['language'] as int;
      if (languageIndex >= 0 && languageIndex < AppLanguage.values.length) {
        _language = AppLanguage.values[languageIndex];
        await _prefs?.setInt(_languageKey, _language.index);
      }
    }

    if (settings.containsKey('isFirstLaunch')) {
      _isFirstLaunch = settings['isFirstLaunch'] as bool;
      await _prefs?.setBool(_isFirstLaunchKey, _isFirstLaunch);
    }

    notifyListeners();
  }

  // Get system brightness
  bool isSystemDark(BuildContext context) {
    return MediaQuery.of(context).platformBrightness == Brightness.dark;
  }

  // Get effective theme mode based on system settings
  bool isDarkMode(BuildContext context) {
    switch (_themeMode) {
      case AppThemeMode.light:
        return false;
      case AppThemeMode.dark:
        return true;
      case AppThemeMode.system:
        return isSystemDark(context);
    }
  }

  // Get text direction based on language
  TextDirection get textDirection {
    switch (_language) {
      case AppLanguage.arabic:
        return TextDirection.rtl;
      case AppLanguage.english:
        return TextDirection.ltr;
    }
  }

  // Check if current language is RTL
  bool get isRTL => _language == AppLanguage.arabic;
}
