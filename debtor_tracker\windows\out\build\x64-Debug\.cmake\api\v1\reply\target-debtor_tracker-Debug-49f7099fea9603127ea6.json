{"artifacts": [{"path": "runner/debtor_tracker.exe"}, {"path": "runner/debtor_tracker.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "add_dependencies", "target_compile_options", "apply_standard_settings", "target_compile_definitions", "add_definitions", "target_include_directories", "target_compile_features"], "files": ["runner/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 9, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 75, "parent": 2}, {"command": 2, "file": 0, "line": 35, "parent": 0}, {"command": 2, "file": 0, "line": 36, "parent": 0}, {"command": 3, "file": 0, "line": 40, "parent": 0}, {"command": 5, "file": 0, "line": 21, "parent": 0}, {"command": 4, "file": 1, "line": 42, "parent": 7}, {"command": 4, "file": 1, "line": 43, "parent": 7}, {"command": 6, "file": 0, "line": 24, "parent": 0}, {"command": 6, "file": 0, "line": 28, "parent": 0}, {"command": 6, "file": 0, "line": 25, "parent": 0}, {"command": 6, "file": 0, "line": 26, "parent": 0}, {"command": 6, "file": 0, "line": 27, "parent": 0}, {"command": 6, "file": 0, "line": 31, "parent": 0}, {"command": 7, "file": 1, "line": 33, "parent": 2}, {"command": 6, "file": 1, "line": 45, "parent": 7}, {"command": 6, "file": 1, "line": 44, "parent": 7}, {"command": 8, "file": 0, "line": 37, "parent": 0}, {"command": 9, "file": 1, "line": 41, "parent": 7}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1"}, {"backtrace": 8, "fragment": "/W4"}, {"backtrace": 8, "fragment": "/WX"}, {"backtrace": 8, "fragment": "/wd\\\"4100\\\""}, {"backtrace": 9, "fragment": "/EHsc"}, {"fragment": "-std:c++17"}], "defines": [{"backtrace": 10, "define": "FLUTTER_VERSION=\"1.0.0+1\""}, {"backtrace": 11, "define": "FLUTTER_VERSION_BUILD=1"}, {"backtrace": 12, "define": "FLUTTER_VERSION_MAJOR=1"}, {"backtrace": 13, "define": "FLUTTER_VERSION_MINOR=0"}, {"backtrace": 14, "define": "FLUTTER_VERSION_PATCH=0"}, {"backtrace": 15, "define": "NOMINMAX"}, {"backtrace": 16, "define": "UNICODE"}, {"backtrace": 17, "define": "_DEBUG"}, {"backtrace": 18, "define": "_HAS_EXCEPTIONS=0"}, {"backtrace": 16, "define": "_UNICODE"}], "includes": [{"backtrace": 19, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/cpp_client_wrapper/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/connectivity_plus/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_windows/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/gal/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/local_auth_windows/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/printing/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/share_plus/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/windows/include"}], "language": "CXX", "languageStandard": {"backtraces": [20], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4]}, {"compileCommandFragments": [{"fragment": "-DWIN32 -D_DEBUG"}], "defines": [{"backtrace": 10, "define": "FLUTTER_VERSION=\"1.0.0+1\""}, {"backtrace": 11, "define": "FLUTTER_VERSION_BUILD=1"}, {"backtrace": 12, "define": "FLUTTER_VERSION_MAJOR=1"}, {"backtrace": 13, "define": "FLUTTER_VERSION_MINOR=0"}, {"backtrace": 14, "define": "FLUTTER_VERSION_PATCH=0"}, {"backtrace": 15, "define": "NOMINMAX"}, {"backtrace": 16, "define": "UNICODE"}, {"backtrace": 17, "define": "_DEBUG"}, {"backtrace": 18, "define": "_HAS_EXCEPTIONS=0"}, {"backtrace": 16, "define": "_UNICODE"}], "includes": [{"backtrace": 19, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/cpp_client_wrapper/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/audioplayers_windows/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/connectivity_plus/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/file_selector_windows/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_windows/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/gal/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/local_auth_windows/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/printing/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/share_plus/windows/include"}, {"backtrace": 0, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/windows/include"}], "language": "RC", "sourceIndexes": [5]}], "dependencies": [{"backtrace": 4, "id": "flutter_wrapper_app::@2bb09fbb03a373b2cb34"}, {"backtrace": 6, "id": "flutter_assemble::@2bb09fbb03a373b2cb34"}, {"backtrace": 0, "id": "audioplayers_windows_plugin::@6f00b843ae6f76675cd2"}, {"backtrace": 0, "id": "connectivity_plus_plugin::@6eaccbd1f84c59b7458d"}, {"backtrace": 0, "id": "flutter_secure_storage_windows_plugin::@ef50a4a009d72e8219ec"}, {"backtrace": 0, "id": "file_selector_windows_plugin::@24329985f05541c6c489"}, {"backtrace": 0, "id": "gal_plugin::@48c3c4013a15203650f9"}, {"backtrace": 0, "id": "local_auth_windows_plugin::@bb74027535e939cb2603"}, {"backtrace": 0, "id": "permission_handler_windows_plugin::@15851dc22c812b45487c"}, {"backtrace": 0, "id": "printing_plugin::@3f1d3ea8e3fd99684a01"}, {"backtrace": 0, "id": "share_plus_plugin::@1b881e75852d57e06a99"}, {"backtrace": 0, "id": "url_launcher_windows_plugin::@82e8e937a0da8e6132e4"}], "id": "debtor_tracker::@056c4f61b0156d1d9b06", "install": {"destinations": [{"backtrace": 3, "path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/out/install/x64-Debug"}], "prefix": {"path": "C:/Users/<USER>/Desktop/debtor/debtor_tracker/windows/out/install/x64-Debug"}}, "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:windows", "role": "flags"}, {"backtrace": 4, "fragment": "..\\flutter\\flutter_wrapper_app.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "dwmapi.lib", "role": "libraries"}, {"backtrace": 0, "fragment": "..\\plugins\\audioplayers_windows\\audioplayers_windows_plugin.lib", "role": "libraries"}, {"backtrace": 0, "fragment": "..\\plugins\\connectivity_plus\\connectivity_plus_plugin.lib", "role": "libraries"}, {"backtrace": 0, "fragment": "..\\plugins\\file_selector_windows\\file_selector_windows_plugin.lib", "role": "libraries"}, {"backtrace": 0, "fragment": "..\\plugins\\flutter_secure_storage_windows\\flutter_secure_storage_windows_plugin.lib", "role": "libraries"}, {"backtrace": 0, "fragment": "..\\plugins\\gal\\gal_plugin.lib", "role": "libraries"}, {"backtrace": 0, "fragment": "..\\plugins\\local_auth_windows\\local_auth_windows_plugin.lib", "role": "libraries"}, {"backtrace": 0, "fragment": "..\\plugins\\permission_handler_windows\\permission_handler_windows_plugin.lib", "role": "libraries"}, {"backtrace": 0, "fragment": "..\\plugins\\printing\\printing_plugin.lib", "role": "libraries"}, {"backtrace": 0, "fragment": "..\\plugins\\share_plus\\share_plus_plugin.lib", "role": "libraries"}, {"backtrace": 0, "fragment": "..\\plugins\\url_launcher_windows\\url_launcher_windows_plugin.lib", "role": "libraries"}, {"fragment": "..\\..\\..\\..\\flutter\\ephemeral\\flutter_windows.dll.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "debtor_tracker", "nameOnDisk": "debtor_tracker.exe", "paths": {"build": "runner", "source": "runner"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5]}, {"name": "", "sourceIndexes": [6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "runner/flutter_window.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "runner/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "runner/utils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "runner/win32_window.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "flutter/generated_plugin_registrant.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "runner/Runner.rc", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "runner/runner.exe.manifest", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}